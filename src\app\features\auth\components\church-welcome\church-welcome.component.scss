.welcome-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.loading-state {
  text-align: center;
  color: white;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
  }

  p {
    font-size: 1.1rem;
    margin: 0;
  }
}

.welcome-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  padding: 40px;
  width: 100%;
  max-width: 600px;
  text-align: center;
}

.header {
  margin-bottom: 30px;

  .church-info {
    h1 {
      color: #333;
      font-size: 2.2rem;
      font-weight: 700;
      margin-bottom: 8px;
    }

    .church-address {
      color: #666;
      font-size: 1rem;
      margin-bottom: 12px;
    }

    .admin-welcome {
      color: #667eea;
      font-size: 1.1rem;
      font-weight: 500;
      margin: 0;
    }
  }
}

.progress-container {
  margin-bottom: 40px;

  .progress-bar {
    width: 100%;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #667eea, #764ba2);
      border-radius: 4px;
      transition: width 0.3s ease;
    }
  }

  .progress-text {
    color: #666;
    font-size: 0.9rem;
    font-weight: 500;
  }
}

.step-content {
  margin-bottom: 40px;

  .step-icon {
    font-size: 4rem;
    margin-bottom: 20px;
  }

  h2 {
    color: #333;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 12px;
  }

  > p {
    color: #666;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 30px;
  }
}

.step-details {
  .feature-list {
    display: grid;
    gap: 16px;
    margin-top: 20px;

    .feature-item {
      display: flex;
      align-items: center;
      text-align: left;
      padding: 12px;
      background: #f8f9ff;
      border-radius: 8px;

      .feature-icon {
        font-size: 1.2rem;
        margin-right: 12px;
      }
    }
  }

  .info-box {
    background: #f8f9ff;
    border: 2px solid #e8ecff;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 20px;

    h4 {
      color: #333;
      margin-bottom: 12px;
      font-size: 1.2rem;
    }

    .church-code {
      background: white;
      border: 2px dashed #667eea;
      border-radius: 8px;
      padding: 16px;
      font-family: 'Courier New', monospace;
      font-size: 1.4rem;
      font-weight: bold;
      color: #667eea;
      margin-bottom: 12px;
      letter-spacing: 2px;
    }

    .code-description {
      color: #666;
      font-size: 0.95rem;
      margin: 0;
    }
  }

  .settings-preview {
    background: #f8f9ff;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;

    .setting-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #e8ecff;

      &:last-child {
        border-bottom: none;
      }

      .setting-label {
        font-weight: 500;
        color: #333;
      }

      .setting-value {
        color: #667eea;
        font-weight: 500;
      }
    }
  }

  .completion-message {
    background: linear-gradient(135deg, #e8f5e8, #f0fff0);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 20px;

    h4 {
      color: #2e7d32;
      margin-bottom: 12px;
      font-size: 1.3rem;
    }

    p {
      color: #388e3c;
      margin: 0;
      font-size: 1rem;
    }
  }

  .quick-actions {
    h5 {
      color: #333;
      margin-bottom: 16px;
      font-size: 1.1rem;
    }

    .action-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;

      .action-card {
        background: white;
        border: 2px solid #e8ecff;
        border-radius: 12px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;

        &:hover {
          border-color: #667eea;
          transform: translateY(-2px);
          box-shadow: 0 8px 20px rgba(102, 126, 234, 0.15);
        }

        .action-icon {
          font-size: 2rem;
          display: block;
          margin-bottom: 8px;
        }

        .action-title {
          color: #333;
          font-weight: 500;
          font-size: 0.95rem;
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 12px;
  }
}

.navigation {
  display: flex;
  align-items: center;
  gap: 16px;

  .nav-spacer {
    flex: 1;
  }

  .btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;

    &.btn-primary {
      background: #667eea;
      color: white;

      &:hover {
        background: #5a6fd8;
        transform: translateY(-1px);
      }
    }

    &.btn-secondary {
      background: #f5f5f5;
      color: #333;

      &:hover {
        background: #e0e0e0;
      }
    }

    &.btn-outline {
      background: transparent;
      color: #667eea;
      border: 2px solid #667eea;

      &:hover {
        background: #667eea;
        color: white;
      }
    }
  }
}

.skip-option {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;

  .btn-link {
    background: none;
    border: none;
    color: #666;
    font-size: 0.9rem;
    cursor: pointer;
    text-decoration: underline;

    &:hover {
      color: #333;
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .welcome-container {
    padding: 10px;
  }

  .welcome-card {
    padding: 30px 20px;
  }

  .step-content {
    .step-icon {
      font-size: 3rem;
    }

    h2 {
      font-size: 1.5rem;
    }
  }

  .step-details {
    .quick-actions .action-grid {
      grid-template-columns: 1fr;
    }
  }

  .navigation {
    flex-direction: column;
    gap: 12px;

    .btn {
      width: 100%;
    }

    .nav-spacer {
      display: none;
    }
  }
}
