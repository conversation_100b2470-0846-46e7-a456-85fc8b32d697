import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { Location } from '@angular/common';
import { Component } from '@angular/core';
import { LandingComponent } from './landing.component';
import { provideRouter } from '@angular/router';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

// Mock components for testing
@Component({ template: '' })
class MockLoginComponent { }

@Component({ template: '' })
class MockSignupComponent { }

describe('LandingComponent', () => {
  let component: LandingComponent;
  let fixture: ComponentFixture<LandingComponent>;
  let router: Router;
  let location: Location;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        LandingComponent,
        BrowserAnimationsModule
      ],
      providers: [
        provideRouter([
          { path: '', component: LandingComponent },
          { path: 'auth/login', component: MockLoginComponent },
          { path: 'auth/signup', component: MockSignupComponent }
        ])
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(LandingComponent);
    component = fixture.componentInstance;
    router = TestBed.inject(Router);
    location = TestBed.inject(Location);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have app name from environment', () => {
    expect(component.appName).toBe('Flockin Dashboard');
  });

  it('should have features array', () => {
    expect(component.features).toBeDefined();
    expect(component.features.length).toBeGreaterThan(0);
  });

  it('should navigate to login when login button is clicked', async () => {
    const loginButton = fixture.debugElement.nativeElement.querySelector('button[routerLink="/auth/login"]');
    expect(loginButton).toBeTruthy();
    
    loginButton.click();
    await fixture.whenStable();
    
    expect(location.path()).toBe('/auth/login');
  });

  it('should navigate to signup when get started button is clicked', async () => {
    const signupButton = fixture.debugElement.nativeElement.querySelector('button[routerLink="/auth/signup"]');
    expect(signupButton).toBeTruthy();
    
    signupButton.click();
    await fixture.whenStable();
    
    expect(location.path()).toBe('/auth/signup');
  });

  it('should scroll to features section', () => {
    // Create a mock element
    const mockElement = document.createElement('div');
    mockElement.id = 'features';
    mockElement.scrollIntoView = jasmine.createSpy('scrollIntoView');
    document.body.appendChild(mockElement);

    component.scrollToFeatures();

    expect(mockElement.scrollIntoView).toHaveBeenCalledWith({ behavior: 'smooth' });

    // Clean up
    document.body.removeChild(mockElement);
  });

  it('should scroll to testimonials section', () => {
    // Create a mock element
    const mockElement = document.createElement('div');
    mockElement.id = 'testimonials';
    mockElement.scrollIntoView = jasmine.createSpy('scrollIntoView');
    document.body.appendChild(mockElement);

    component.scrollToTestimonials();

    expect(mockElement.scrollIntoView).toHaveBeenCalledWith({ behavior: 'smooth' });

    // Clean up
    document.body.removeChild(mockElement);
  });

  it('should render footer', () => {
    const footer = fixture.debugElement.nativeElement.querySelector('.footer');
    expect(footer).toBeTruthy();
  });

  it('should render hero section with colorful gradient', () => {
    const hero = fixture.debugElement.nativeElement.querySelector('.hero');
    expect(hero).toBeTruthy();
    
    const computedStyle = window.getComputedStyle(hero);
    expect(computedStyle.background).toContain('gradient');
  });

  it('should render feature cards with different colored icons', () => {
    const featureCards = fixture.debugElement.nativeElement.querySelectorAll('.feature-card');
    expect(featureCards.length).toBeGreaterThan(0);
    
    const featureIcons = fixture.debugElement.nativeElement.querySelectorAll('.feature-icon');
    expect(featureIcons.length).toBeGreaterThan(0);
  });
});
