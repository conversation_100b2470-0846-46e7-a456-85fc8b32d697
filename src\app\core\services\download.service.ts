import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

export interface DownloadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface DownloadResult {
  success: boolean;
  filename: string;
  size?: number;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class DownloadService {

  // Dropbox direct download URL for the APK
  private readonly APK_DOWNLOAD_URL = 'https://www.dropbox.com/scl/fi/luwssib6q3gpsv24jihsc/flockin-app.apk?rlkey=mxzu0q9lphx7u4xxygyucqxg4&st=10on5ijg&dl=1';
  private readonly APK_FILENAME = 'flockin-app.apk';

  constructor() {}

  /**
   * Download APK file from Dropbox using direct download approach
   */
  downloadAPK(filename: string = this.APK_FILENAME): Observable<DownloadResult> {
    const url = this.APK_DOWNLOAD_URL;
    console.log('📥 Starting direct APK download from Dropbox:', url);

    return new Observable<DownloadResult>(observer => {
      try {
        // Create download link element
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';
        link.target = '_blank'; // Open in new tab as fallback

        // Add to DOM temporarily
        document.body.appendChild(link);

        // Trigger download
        link.click();

        // Clean up
        document.body.removeChild(link);

        console.log('✅ Download initiated successfully');

        // Return success immediately since we can't track direct downloads
        observer.next({
          success: true,
          filename: filename,
          size: undefined // Size unknown for direct downloads
        });
        observer.complete();

      } catch (error) {
        console.error('❌ Error initiating download:', error);
        observer.error({
          success: false,
          filename: filename,
          error: 'Failed to initiate download'
        });
      }
    });
  }

  /**
   * Check if APK file is available on Dropbox (simplified for CORS limitations)
   */
  checkAPKAvailability(): Observable<{exists: boolean, size?: number}> {
    console.log('🔍 Dropbox APK availability check (assuming available due to CORS limitations)');

    return new Observable(observer => {
      // Since we can't make CORS requests to Dropbox, we'll assume the file exists
      // This is safe because if the file doesn't exist, the download will simply fail gracefully
      setTimeout(() => {
        console.log('✅ Assuming Dropbox APK is available');
        observer.next({
          exists: true,
          size: undefined // Size unknown due to CORS
        });
        observer.complete();
      }, 100); // Small delay to simulate async operation
    });
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get device info for download analytics
   */
  getDeviceInfo(): {platform: string, userAgent: string, isMobile: boolean} {
    const userAgent = navigator.userAgent;
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);

    let platform = 'Unknown';
    if (/Android/i.test(userAgent)) platform = 'Android';
    else if (/iPhone|iPad|iPod/i.test(userAgent)) platform = 'iOS';
    else if (/Windows/i.test(userAgent)) platform = 'Windows';
    else if (/Mac/i.test(userAgent)) platform = 'macOS';
    else if (/Linux/i.test(userAgent)) platform = 'Linux';

    return { platform, userAgent, isMobile };
  }

}