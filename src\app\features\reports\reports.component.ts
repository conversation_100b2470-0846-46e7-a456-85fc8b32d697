import { Component, OnInit, OnDestroy, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatIconModule } from '@angular/material/icon';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

import { AuthService } from '@core/services/auth.service';
import { AttendanceService, WeeklyReport, MonthlyReport, AttendanceTrends } from '@core/services/attendance.service';
import { AttendanceChartComponent, ChartData } from '@shared/components/attendance-chart/attendance-chart.component';
import { ChurchService } from '@core/services/church.service';
import { Church } from '@shared/models/church.model';

export type ReportType = 'weekly' | 'monthly' | 'trends';
export type DateRange = 'last_week' | 'last_month' | 'last_3_months' | 'last_6_months' | 'custom';

@Component({
  selector: 'app-reports',
  standalone: true,
  imports: [CommonModule, FormsModule, AttendanceChartComponent, MatButtonModule, MatMenuModule, MatIconModule],
  templateUrl: './reports.component.html',
  styleUrl: './reports.component.scss'
})
export class ReportsComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Signals for reactive UI
  isLoading = signal<boolean>(false);
  error = signal<string | null>(null);
  selectedReportType = signal<ReportType>('weekly');
  selectedDateRange = signal<DateRange>('last_month');
  customStartDate = signal<string>('');
  customEndDate = signal<string>('');

  // Advanced filtering options
  showAdvancedFilters = signal<boolean>(false);
  filterByVerificationStatus = signal<'all' | 'verified' | 'unverified' | 'flagged'>('all');
  filterByServiceType = signal<'all' | 'sunday' | 'weekday' | 'special'>('all');
  minimumAttendance = signal<number>(0);
  showOnlyActiveMembers = signal<boolean>(false);

  // Data signals
  currentUser = signal<any>(null);
  currentChurch = signal<Church | null>(null);
  weeklyReport = signal<WeeklyReport | null>(null);
  monthlyReport = signal<MonthlyReport | null>(null);
  trendsReport = signal<AttendanceTrends | null>(null);

  // Computed properties
  dateRangeOptions = computed(() => [
    { value: 'last_week', label: 'Last Week' },
    { value: 'last_month', label: 'Last Month' },
    { value: 'last_3_months', label: 'Last 3 Months' },
    { value: 'last_6_months', label: 'Last 6 Months' },
    { value: 'custom', label: 'Custom Range' }
  ]);

  reportTypeOptions = computed(() => [
    { value: 'weekly', label: 'Weekly Report', icon: 'calendar-week' },
    { value: 'monthly', label: 'Monthly Report', icon: 'calendar-month' },
    { value: 'trends', label: 'Trends & Analytics', icon: 'trending-up' }
  ]);

  isCustomDateRange = computed(() => this.selectedDateRange() === 'custom');

  // Filter options computed properties
  verificationStatusOptions = computed(() => [
    { value: 'all', label: 'All Records' },
    { value: 'verified', label: 'Verified Only' },
    { value: 'unverified', label: 'Unverified Only' },
    { value: 'flagged', label: 'Flagged Only' }
  ]);

  serviceTypeOptions = computed(() => [
    { value: 'all', label: 'All Services' },
    { value: 'sunday', label: 'Sunday Services' },
    { value: 'weekday', label: 'Weekday Services' },
    { value: 'special', label: 'Special Events' }
  ]);

  // Filtered data computed properties
  filteredWeeklyData = computed(() => {
    const report = this.weeklyReport();
    if (!report) return null;

    let filteredData = [...report.weeklyData];

    // Apply minimum attendance filter
    const minAttendance = this.minimumAttendance();
    if (minAttendance > 0) {
      filteredData = filteredData.filter(week => week.totalAttendance >= minAttendance);
    }

    // Apply verification status filter
    const verificationFilter = this.filterByVerificationStatus();
    if (verificationFilter !== 'all') {
      filteredData = filteredData.filter(week => {
        switch (verificationFilter) {
          case 'verified':
            return week.verificationRate >= 80; // 80% or higher verification rate
          case 'unverified':
            return week.verificationRate < 50; // Less than 50% verification rate
          case 'flagged':
            return week.flaggedCount > 0; // Has flagged records
          default:
            return true;
        }
      });
    }

    return {
      ...report,
      weeklyData: filteredData,
      summary: {
        ...report.summary,
        totalAttendance: filteredData.reduce((sum, week) => sum + week.totalAttendance, 0),
        averageWeeklyAttendance: filteredData.length > 0 ?
          filteredData.reduce((sum, week) => sum + week.totalAttendance, 0) / filteredData.length : 0
      }
    };
  });

  filteredMonthlyData = computed(() => {
    const report = this.monthlyReport();
    if (!report) return null;

    let filteredData = [...report.monthlyData];

    // Apply minimum attendance filter
    const minAttendance = this.minimumAttendance();
    if (minAttendance > 0) {
      filteredData = filteredData.filter(month => month.totalAttendance >= minAttendance);
    }

    // Apply verification status filter
    const verificationFilter = this.filterByVerificationStatus();
    if (verificationFilter !== 'all') {
      filteredData = filteredData.filter(month => {
        switch (verificationFilter) {
          case 'verified':
            return month.verificationRate >= 80;
          case 'unverified':
            return month.verificationRate < 50;
          case 'flagged':
            return month.flaggedCount > 0;
          default:
            return true;
        }
      });
    }

    // Apply service type filter
    const serviceFilter = this.filterByServiceType();
    if (serviceFilter === 'sunday') {
      filteredData = filteredData.filter(month => month.weekendServices > 0);
    }

    return {
      ...report,
      monthlyData: filteredData,
      summary: {
        ...report.summary,
        totalAttendance: filteredData.reduce((sum, month) => sum + month.totalAttendance, 0),
        averageMonthlyAttendance: filteredData.length > 0 ?
          filteredData.reduce((sum, month) => sum + month.totalAttendance, 0) / filteredData.length : 0
      }
    };
  });

  currentReport = computed(() => {
    const reportType = this.selectedReportType();
    switch (reportType) {
      case 'weekly':
        return this.weeklyReport();
      case 'monthly':
        return this.monthlyReport();
      case 'trends':
        return this.trendsReport();
      default:
        return null;
    }
  });

  // Chart data computed properties (using filtered data)
  weeklyChartData = computed(() => {
    const report = this.filteredWeeklyData();
    if (!report) return null;

    return {
      labels: report.weeklyData.map(week => {
        const date = new Date(week.weekStart);
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      }),
      datasets: [
        {
          label: 'Total Attendance',
          data: report.weeklyData.map(week => week.totalAttendance),
          backgroundColor: 'rgba(102, 126, 234, 0.1)',
          borderColor: 'rgba(102, 126, 234, 1)',
          borderWidth: 2,
          fill: true,
        },
        {
          label: 'Unique Members',
          data: report.weeklyData.map(week => week.uniqueMembers),
          backgroundColor: 'rgba(72, 187, 120, 0.1)',
          borderColor: 'rgba(72, 187, 120, 1)',
          borderWidth: 2,
          fill: false,
        }
      ]
    } as ChartData;
  });

  monthlyChartData = computed(() => {
    const report = this.filteredMonthlyData();
    if (!report) return null;

    return {
      labels: report.monthlyData.map(month => {
        const [year, monthNum] = month.month.split('-');
        const date = new Date(parseInt(year), parseInt(monthNum) - 1);
        return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
      }),
      datasets: [
        {
          label: 'Total Attendance',
          data: report.monthlyData.map(month => month.totalAttendance),
          backgroundColor: 'rgba(102, 126, 234, 0.1)',
          borderColor: 'rgba(102, 126, 234, 1)',
          borderWidth: 2,
          fill: true,
        },
        {
          label: 'Unique Members',
          data: report.monthlyData.map(month => month.uniqueMembers),
          backgroundColor: 'rgba(72, 187, 120, 0.1)',
          borderColor: 'rgba(72, 187, 120, 1)',
          borderWidth: 2,
          fill: false,
        }
      ]
    } as ChartData;
  });

  verificationChartData = computed(() => {
    const reportType = this.selectedReportType();
    let data: any[] = [];

    if (reportType === 'weekly' && this.weeklyReport()) {
      data = this.weeklyReport()!.weeklyData;
    } else if (reportType === 'monthly' && this.monthlyReport()) {
      data = this.monthlyReport()!.monthlyData;
    }

    if (data.length === 0) return null;

    return {
      labels: data.map((item: any) => {
        if (reportType === 'weekly') {
          const date = new Date(item.weekStart);
          return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        } else {
          const [year, monthNum] = item.month.split('-');
          const date = new Date(parseInt(year), parseInt(monthNum) - 1);
          return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
        }
      }),
      datasets: [
        {
          label: 'Verification Rate (%)',
          data: data.map((item: any) => item.verificationRate),
          backgroundColor: 'rgba(237, 137, 54, 0.1)',
          borderColor: 'rgba(237, 137, 54, 1)',
          borderWidth: 2,
          fill: true,
        }
      ]
    } as ChartData;
  });

  constructor(
    private authService: AuthService,
    private attendanceService: AttendanceService,
    private churchService: ChurchService
  ) { }

  ngOnInit(): void {
    // Get current user
    this.authService.currentUser$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(user => {
      this.currentUser.set(user);
      if (user?.churchId) {
        // Load church data
        this.churchService.getChurch(user.churchId).pipe(
          takeUntil(this.destroy$)
        ).subscribe({
          next: (church) => {
            this.currentChurch.set(church);
          },
          error: (error) => {
            console.error('Failed to load church data:', error);
          }
        });

        this.loadReport();
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Event handlers
  onReportTypeChange(reportType: ReportType): void {
    this.selectedReportType.set(reportType);
    this.loadReport();
  }

  onDateRangeChange(dateRange: DateRange): void {
    this.selectedDateRange.set(dateRange);
    if (dateRange !== 'custom') {
      this.loadReport();
    }
  }

  onCustomDateChange(): void {
    if (this.customStartDate() && this.customEndDate()) {
      this.loadReport();
    }
  }

  onRefreshReport(): void {
    this.loadReport();
  }

  onToggleAdvancedFilters(): void {
    this.showAdvancedFilters.set(!this.showAdvancedFilters());
  }

  onVerificationFilterChange(status: string): void {
    this.filterByVerificationStatus.set(status as any);
  }

  onServiceTypeFilterChange(type: string): void {
    this.filterByServiceType.set(type as any);
  }

  onMinimumAttendanceChange(value: number): void {
    this.minimumAttendance.set(value);
  }

  onActiveOnlyToggle(): void {
    this.showOnlyActiveMembers.set(!this.showOnlyActiveMembers());
  }

  onResetFilters(): void {
    this.filterByVerificationStatus.set('all');
    this.filterByServiceType.set('all');
    this.minimumAttendance.set(0);
    this.showOnlyActiveMembers.set(false);
  }

  onExportReport(format: 'pdf' | 'excel' | 'csv'): void {
    const user = this.currentUser();
    if (!user?.churchId) return;

    const { startDate, endDate } = this.getDateRange();
    const reportType = this.selectedReportType();

    this.attendanceService.getExportData(
      user.churchId,
      format,
      startDate,
      endDate,
      reportType
    ).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (exportData) => {
        this.downloadExport(exportData, format);
      },
      error: (error) => {
        console.error('Export failed:', error);
        this.error.set('Failed to export report. Please try again.');
      }
    });
  }

  // Private methods
  private loadReport(): void {
    const user = this.currentUser();
    if (!user?.churchId) return;

    this.isLoading.set(true);
    this.error.set(null);

    const { startDate, endDate } = this.getDateRange();
    const reportType = this.selectedReportType();

    switch (reportType) {
      case 'weekly':
        this.attendanceService.getWeeklyReport(user.churchId, startDate, endDate).pipe(
          takeUntil(this.destroy$)
        ).subscribe({
          next: (data: WeeklyReport) => {
            this.weeklyReport.set(data);
            this.isLoading.set(false);
          },
          error: (error: any) => {
            console.error('Failed to load weekly report:', error);
            this.error.set('Failed to load report. Please try again.');
            this.isLoading.set(false);
          }
        });
        break;
      case 'monthly':
        this.attendanceService.getMonthlyReport(user.churchId, startDate, endDate).pipe(
          takeUntil(this.destroy$)
        ).subscribe({
          next: (data: MonthlyReport) => {
            this.monthlyReport.set(data);
            this.isLoading.set(false);
          },
          error: (error: any) => {
            console.error('Failed to load monthly report:', error);
            this.error.set('Failed to load report. Please try again.');
            this.isLoading.set(false);
          }
        });
        break;
      case 'trends':
        const days = this.calculateDaysFromRange();
        this.attendanceService.getAttendanceTrends(user.churchId, 'monthly', days).pipe(
          takeUntil(this.destroy$)
        ).subscribe({
          next: (data: AttendanceTrends) => {
            this.trendsReport.set(data);
            this.isLoading.set(false);
          },
          error: (error: any) => {
            console.error('Failed to load trends report:', error);
            this.error.set('Failed to load report. Please try again.');
            this.isLoading.set(false);
          }
        });
        break;
    }
  }

  private getDateRange(): { startDate?: Date; endDate?: Date } {
    const dateRange = this.selectedDateRange();
    const now = new Date();

    if (dateRange === 'custom') {
      return {
        startDate: this.customStartDate() ? new Date(this.customStartDate()) : undefined,
        endDate: this.customEndDate() ? new Date(this.customEndDate()) : undefined
      };
    }

    const endDate = new Date(now);
    const startDate = new Date(now);

    switch (dateRange) {
      case 'last_week':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case 'last_month':
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      case 'last_3_months':
        startDate.setMonth(startDate.getMonth() - 3);
        break;
      case 'last_6_months':
        startDate.setMonth(startDate.getMonth() - 6);
        break;
    }

    return { startDate, endDate };
  }

  private calculateDaysFromRange(): number {
    const dateRange = this.selectedDateRange();
    switch (dateRange) {
      case 'last_week': return 7;
      case 'last_month': return 30;
      case 'last_3_months': return 90;
      case 'last_6_months': return 180;
      case 'custom':
        if (this.customStartDate() && this.customEndDate()) {
          const start = new Date(this.customStartDate());
          const end = new Date(this.customEndDate());
          return Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
        }
        return 30;
      default: return 30;
    }
  }

  private downloadExport(exportData: any, format: string): void {
    const timestamp = new Date().toISOString().split('T')[0];
    const reportType = this.selectedReportType();

    let blob: Blob;
    let filename: string;

    switch (format) {
      case 'pdf':
        this.generatePDFReport(exportData, reportType, timestamp);
        return; // PDF is handled differently, no blob needed
      case 'excel':
        const excelContent = this.generateExcelContent(exportData);
        blob = new Blob([excelContent], { type: 'application/vnd.ms-excel' });
        filename = `${reportType}-attendance-report-${timestamp}.xls`;
        break;
      case 'csv':
        const csv = this.convertToCSV(exportData.data);
        blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        filename = `${reportType}-attendance-report-${timestamp}.csv`;
        break;
      default:
        blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        filename = `${reportType}-attendance-report-${timestamp}.json`;
    }

    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }

  private convertToCSV(data: any): string {
    if (!data) return '';

    const reportType = this.selectedReportType();
    const church = this.currentChurch();
    const user = this.currentUser();
    let csvContent = '';

    // Add church and report header
    csvContent += `Church Attendance Report - ${reportType.charAt(0).toUpperCase() + reportType.slice(1)}\n`;
    csvContent += `\n`;

    // Church Information
    if (church) {
      csvContent += `Church Name: ${church.name}\n`;
      if (church.branch) csvContent += `Branch: ${church.branch}\n`;
      csvContent += `Address: ${church.address}\n`;
      if (church.phone) csvContent += `Phone: ${church.phone}\n`;
      if (church.email) csvContent += `Email: ${church.email}\n`;
      csvContent += `Administrator: ${church.adminName}\n`;
      csvContent += `Admin Contact: ${church.adminEmail} | ${church.adminPhone}\n`;
    } else {
      csvContent += `Church ID: ${user?.churchId || 'Unknown'}\n`;
    }

    csvContent += `\n`;
    csvContent += `Generated: ${new Date().toLocaleString()}\n`;
    csvContent += `Generated By: ${user?.name || user?.email || 'System'}\n`;

    const { startDate, endDate } = this.getDateRange();
    if (startDate && endDate) {
      csvContent += `Period: ${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}\n`;
    }
    csvContent += `\n`;

    if (reportType === 'weekly' && this.weeklyReport()) {
      const report = this.weeklyReport()!;
      csvContent += 'Weekly Summary\n';
      csvContent += `Total Attendance,${report.summary.totalAttendance}\n`;
      csvContent += `Average Weekly Attendance,${report.summary.averageWeeklyAttendance.toFixed(1)}\n`;
      csvContent += `Total Weeks,${report.totalWeeks}\n\n`;

      csvContent += 'Week Starting,Total Attendance,Unique Members,Services Held,Verification Rate (%),Avg per Service\n';
      report.weeklyData.forEach(week => {
        csvContent += `${week.weekStart},${week.totalAttendance},${week.uniqueMembers},${week.servicesHeld},${week.verificationRate.toFixed(1)},${week.averagePerService.toFixed(1)}\n`;
      });
    } else if (reportType === 'monthly' && this.monthlyReport()) {
      const report = this.monthlyReport()!;
      csvContent += 'Monthly Summary\n';
      csvContent += `Total Attendance,${report.summary.totalAttendance}\n`;
      csvContent += `Average Monthly Attendance,${report.summary.averageMonthlyAttendance.toFixed(1)}\n`;
      csvContent += `Total Months,${report.totalMonths}\n\n`;

      csvContent += 'Month,Total Attendance,Unique Members,Services Held,Weekend Services,Verification Rate (%),Avg per Service\n';
      report.monthlyData.forEach(month => {
        csvContent += `${month.month},${month.totalAttendance},${month.uniqueMembers},${month.servicesHeld},${month.weekendServices},${month.verificationRate.toFixed(1)},${month.averagePerService.toFixed(1)}\n`;
      });
    } else if (data.records) {
      // Detailed records export
      csvContent += 'Date,Member Name,Check In Time,Check Out Time,Method,Verified,Flagged,Accuracy\n';
      data.records.forEach((record: any) => {
        csvContent += `${record.date},"${record.memberName}",${record.checkInTime},${record.checkOutTime || ''},${record.method},${record.isVerified ? 'Yes' : 'No'},${record.isFlagged ? 'Yes' : 'No'},${record.accuracy || 'N/A'}\n`;
      });
    }

    return csvContent;
  }

  private generatePDFReport(data: any, reportType: string, timestamp: string): void {
    const doc = new jsPDF();
    const church = this.currentChurch();
    const user = this.currentUser();

    // Header with church logo space (if available)
    doc.setFontSize(20);
    doc.setFont('helvetica', 'bold');
    doc.text(`${reportType.charAt(0).toUpperCase() + reportType.slice(1)} Attendance Report`, 20, 25);

    // Church Information Section
    doc.setFontSize(14);
    doc.setFont('helvetica', 'bold');
    doc.text('Church Information', 20, 45);

    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    let yPosition = 55;

    if (church) {
      doc.text(`Church Name: ${church.name}`, 20, yPosition);
      yPosition += 6;

      if (church.branch) {
        doc.text(`Branch: ${church.branch}`, 20, yPosition);
        yPosition += 6;
      }

      doc.text(`Address: ${church.address}`, 20, yPosition);
      yPosition += 6;

      if (church.phone) {
        doc.text(`Phone: ${church.phone}`, 20, yPosition);
        yPosition += 6;
      }

      if (church.email) {
        doc.text(`Email: ${church.email}`, 20, yPosition);
        yPosition += 6;
      }

      // Admin Information
      doc.text(`Administrator: ${church.adminName}`, 20, yPosition);
      yPosition += 6;
      doc.text(`Admin Contact: ${church.adminEmail} | ${church.adminPhone}`, 20, yPosition);
      yPosition += 6;

      // Service Times
      if (church.serviceTimes && church.serviceTimes.length > 0) {
        doc.text('Service Times:', 20, yPosition);
        yPosition += 6;
        church.serviceTimes.forEach(service => {
          const timeInfo = service.day
            ? `${service.day.charAt(0).toUpperCase() + service.day.slice(1)}s: ${service.startTime} - ${service.endTime} (${service.name})`
            : `${service.date}: ${service.startTime} - ${service.endTime} (${service.name})`;
          doc.text(`  • ${timeInfo}`, 25, yPosition);
          yPosition += 5;
        });
        yPosition += 3;
      }
    } else {
      doc.text(`Church ID: ${user?.churchId || 'Unknown'}`, 20, yPosition);
      yPosition += 6;
    }

    // Report Information
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('Report Details', 20, yPosition + 5);
    yPosition += 15;

    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.text(`Report Type: ${reportType.charAt(0).toUpperCase() + reportType.slice(1)} Report`, 20, yPosition);
    yPosition += 6;
    doc.text(`Generated: ${new Date().toLocaleString()}`, 20, yPosition);
    yPosition += 6;
    doc.text(`Generated By: ${user?.name || user?.email || 'System'}`, 20, yPosition);
    yPosition += 6;

    // Date Range
    const { startDate, endDate } = this.getDateRange();
    if (startDate && endDate) {
      doc.text(`Period: ${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`, 20, yPosition);
      yPosition += 6;
    }
    yPosition += 10;

    // Add summary statistics if available
    if (data.summary) {
      doc.setFontSize(14);
      doc.text('Summary Statistics', 20, yPosition);
      yPosition += 10;

      doc.setFontSize(10);
      doc.text(`Total Attendance: ${data.summary.totalAttendance || 0}`, 20, yPosition);
      yPosition += 8;
      doc.text(`Unique Members: ${data.summary.uniqueMembers || 0}`, 20, yPosition);
      yPosition += 8;
      doc.text(`Average per Service: ${data.summary.averagePerService || 0}`, 20, yPosition);
      yPosition += 15;
    }

    // Add table data
    if (data.weeklyData || data.monthlyData || data.records) {
      const tableData = this.formatDataForPDF(data, reportType);

      autoTable(doc, {
        head: [tableData.headers],
        body: tableData.rows,
        startY: yPosition,
        styles: { fontSize: 8 },
        headStyles: { fillColor: [103, 126, 234] },
        margin: { top: 20 }
      });
    }

    // Save the PDF
    const filename = `${reportType}-attendance-report-${timestamp}.pdf`;
    doc.save(filename);
  }

  private formatDataForPDF(data: any, reportType: string): { headers: string[], rows: any[][] } {
    if (reportType === 'weekly' && data.weeklyData) {
      return {
        headers: ['Week', 'Total Attendance', 'Unique Members', 'Services', 'Avg per Service'],
        rows: data.weeklyData.map((week: any) => [
          week.week,
          week.totalAttendance,
          week.uniqueMembers,
          week.servicesHeld,
          week.averagePerService?.toFixed(1) || '0'
        ])
      };
    } else if (reportType === 'monthly' && data.monthlyData) {
      return {
        headers: ['Month', 'Total Attendance', 'Unique Members', 'Services', 'Verification Rate', 'Avg per Service'],
        rows: data.monthlyData.map((month: any) => [
          month.month,
          month.totalAttendance,
          month.uniqueMembers,
          month.servicesHeld,
          `${month.verificationRate?.toFixed(1) || 0}%`,
          month.averagePerService?.toFixed(1) || '0'
        ])
      };
    } else if (data.records) {
      return {
        headers: ['Date', 'Member Name', 'Check In', 'Check Out', 'Method', 'Verified'],
        rows: data.records.map((record: any) => [
          record.date,
          record.memberName,
          record.checkInTime,
          record.checkOutTime || '',
          record.method,
          record.isVerified ? 'Yes' : 'No'
        ])
      };
    }

    return { headers: [], rows: [] };
  }

  private generateExcelContent(data: any): string {
    const reportType = this.selectedReportType();
    const church = this.currentChurch();
    const user = this.currentUser();

    let churchInfo = '';
    if (church) {
      churchInfo = `
        <tr><td colspan="6"><strong>Church:</strong> ${church.name}</td></tr>`;
      if (church.branch) {
        churchInfo += `<tr><td colspan="6"><strong>Branch:</strong> ${church.branch}</td></tr>`;
      }
      churchInfo += `
        <tr><td colspan="6"><strong>Address:</strong> ${church.address}</td></tr>`;
      if (church.phone) {
        churchInfo += `<tr><td colspan="6"><strong>Phone:</strong> ${church.phone}</td></tr>`;
      }
      if (church.email) {
        churchInfo += `<tr><td colspan="6"><strong>Email:</strong> ${church.email}</td></tr>`;
      }
      churchInfo += `
        <tr><td colspan="6"><strong>Administrator:</strong> ${church.adminName}</td></tr>
        <tr><td colspan="6"><strong>Admin Contact:</strong> ${church.adminEmail} | ${church.adminPhone}</td></tr>`;
    } else {
      churchInfo = `<tr><td colspan="6"><strong>Church ID:</strong> ${user?.churchId || 'Unknown'}</td></tr>`;
    }

    const { startDate, endDate } = this.getDateRange();
    let periodInfo = '';
    if (startDate && endDate) {
      periodInfo = `<tr><td colspan="6"><strong>Period:</strong> ${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}</td></tr>`;
    }

    let excelContent = `
<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="ProgId" content="Excel.Sheet">
    <style>
        .header { font-weight: bold; font-size: 14px; }
        .summary { background-color: #f0f0f0; }
        table { border-collapse: collapse; }
        td, th { border: 1px solid #000; padding: 5px; }
    </style>
</head>
<body>
    <table>
        <tr><td class="header" colspan="6">Church Attendance Report - ${reportType.charAt(0).toUpperCase() + reportType.slice(1)}</td></tr>
        <tr><td colspan="6"></td></tr>
        ${churchInfo}
        <tr><td colspan="6"></td></tr>
        <tr><td colspan="6"><strong>Generated:</strong> ${new Date().toLocaleString()}</td></tr>
        <tr><td colspan="6"><strong>Generated By:</strong> ${user?.name || user?.email || 'System'}</td></tr>
        ${periodInfo}
        <tr><td colspan="6"></td></tr>
        ${this.generateExcelTableContent(data)}
    </table>
</body>
</html>`;

    return excelContent;
  }

  private generateReportContent(data: any): string {
    const reportType = this.selectedReportType();

    if (reportType === 'weekly' && this.weeklyReport()) {
      const report = this.weeklyReport()!;
      return `
        <div class="summary">
            <h3>Summary</h3>
            <div class="stat">Total Attendance: ${report.summary.totalAttendance}</div>
            <div class="stat">Average Weekly: ${report.summary.averageWeeklyAttendance.toFixed(1)}</div>
            <div class="stat">Weeks Analyzed: ${report.totalWeeks}</div>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Week Starting</th>
                    <th>Total Attendance</th>
                    <th>Unique Members</th>
                    <th>Services Held</th>
                    <th>Verification Rate</th>
                    <th>Avg per Service</th>
                </tr>
            </thead>
            <tbody>
                ${report.weeklyData.map(week => `
                    <tr>
                        <td>${new Date(week.weekStart).toLocaleDateString()}</td>
                        <td>${week.totalAttendance}</td>
                        <td>${week.uniqueMembers}</td>
                        <td>${week.servicesHeld}</td>
                        <td>${week.verificationRate.toFixed(1)}%</td>
                        <td>${week.averagePerService.toFixed(1)}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>`;
    } else if (reportType === 'monthly' && this.monthlyReport()) {
      const report = this.monthlyReport()!;
      return `
        <div class="summary">
            <h3>Summary</h3>
            <div class="stat">Total Attendance: ${report.summary.totalAttendance}</div>
            <div class="stat">Average Monthly: ${report.summary.averageMonthlyAttendance.toFixed(1)}</div>
            <div class="stat">Months Analyzed: ${report.totalMonths}</div>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Month</th>
                    <th>Total Attendance</th>
                    <th>Unique Members</th>
                    <th>Services Held</th>
                    <th>Weekend Services</th>
                    <th>Verification Rate</th>
                    <th>Avg per Service</th>
                </tr>
            </thead>
            <tbody>
                ${report.monthlyData.map(month => `
                    <tr>
                        <td>${month.month}</td>
                        <td>${month.totalAttendance}</td>
                        <td>${month.uniqueMembers}</td>
                        <td>${month.servicesHeld}</td>
                        <td>${month.weekendServices}</td>
                        <td>${month.verificationRate.toFixed(1)}%</td>
                        <td>${month.averagePerService.toFixed(1)}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>`;
    }

    return '<p>No report data available</p>';
  }

  private generateExcelTableContent(data: any): string {
    const reportType = this.selectedReportType();

    if (reportType === 'weekly' && this.weeklyReport()) {
      const report = this.weeklyReport()!;
      return `
        <tr class="summary"><td colspan="6">Summary</td></tr>
        <tr><td>Total Attendance</td><td>${report.summary.totalAttendance}</td><td colspan="4"></td></tr>
        <tr><td>Average Weekly</td><td>${report.summary.averageWeeklyAttendance.toFixed(1)}</td><td colspan="4"></td></tr>
        <tr><td>Weeks Analyzed</td><td>${report.totalWeeks}</td><td colspan="4"></td></tr>
        <tr><td colspan="6"></td></tr>
        <tr><th>Week Starting</th><th>Total Attendance</th><th>Unique Members</th><th>Services Held</th><th>Verification Rate</th><th>Avg per Service</th></tr>
        ${report.weeklyData.map(week => `
          <tr>
            <td>${new Date(week.weekStart).toLocaleDateString()}</td>
            <td>${week.totalAttendance}</td>
            <td>${week.uniqueMembers}</td>
            <td>${week.servicesHeld}</td>
            <td>${week.verificationRate.toFixed(1)}%</td>
            <td>${week.averagePerService.toFixed(1)}</td>
          </tr>
        `).join('')}`;
    } else if (reportType === 'monthly' && this.monthlyReport()) {
      const report = this.monthlyReport()!;
      return `
        <tr class="summary"><td colspan="7">Summary</td></tr>
        <tr><td>Total Attendance</td><td>${report.summary.totalAttendance}</td><td colspan="5"></td></tr>
        <tr><td>Average Monthly</td><td>${report.summary.averageMonthlyAttendance.toFixed(1)}</td><td colspan="5"></td></tr>
        <tr><td>Months Analyzed</td><td>${report.totalMonths}</td><td colspan="5"></td></tr>
        <tr><td colspan="7"></td></tr>
        <tr><th>Month</th><th>Total Attendance</th><th>Unique Members</th><th>Services Held</th><th>Weekend Services</th><th>Verification Rate</th><th>Avg per Service</th></tr>
        ${report.monthlyData.map(month => `
          <tr>
            <td>${month.month}</td>
            <td>${month.totalAttendance}</td>
            <td>${month.uniqueMembers}</td>
            <td>${month.servicesHeld}</td>
            <td>${month.weekendServices}</td>
            <td>${month.verificationRate.toFixed(1)}%</td>
            <td>${month.averagePerService.toFixed(1)}</td>
          </tr>
        `).join('')}`;
    }

    return '<tr><td colspan="6">No data available</td></tr>';
  }
}
