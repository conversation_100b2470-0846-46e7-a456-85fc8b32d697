// Material Toolbar overrides for church theme
.header {
  height: 64px !important;
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 1.5rem !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;

  .sidebar-toggle,
  .mobile-menu-toggle {
    color: white !important;

    &:hover {
      background: rgba(255, 255, 255, 0.1) !important;
    }
  }

  .page-title h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: white;
  }
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

// Notification button styling
.notification-btn {
  color: white !important;

  &:hover {
    background: rgba(255, 255, 255, 0.1) !important;
  }
}

// Material Menu styling for user menu
.user-menu {
  .user-menu-trigger {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: white !important;
    background: transparent !important;
    border: none !important;
    padding: 0.5rem 1rem !important;
    border-radius: 8px !important;
    transition: background-color 0.2s ease !important;

    &:hover {
      background: rgba(255, 255, 255, 0.1) !important;
    }

    .user-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      object-fit: cover;
    }

    .user-avatar-placeholder {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.875rem;
      font-weight: 600;
    }

    .user-info {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      text-align: left;

      .user-name {
        font-size: 0.875rem;
        font-weight: 500;
        color: white;
        line-height: 1.2;
      }

      .user-role {
        font-size: 0.75rem;
        color: rgba(255, 255, 255, 0.7);
        text-transform: capitalize;
        line-height: 1.2;
      }

      @media (max-width: 768px) {
        display: none;
      }
    }

    .dropdown-arrow {
      color: rgba(255, 255, 255, 0.7);
    }
  }

}

// Material Menu dropdown styling
::ng-deep .user-dropdown {
  min-width: 220px;
  margin-top: 0.5rem;
  border-radius: 8px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;

  .mat-mdc-menu-content {
    padding: 0.5rem 0 !important;
  }

  .dropdown-header {
    padding: 1rem !important;
    cursor: default !important;
    border-bottom: 1px solid #e2e8f0;

    .user-details {
      .user-name {
        font-size: 0.875rem;
        font-weight: 600;
        color: #1a202c;
        margin-bottom: 0.25rem;
      }

      .user-email {
        font-size: 0.75rem;
        color: #718096;
      }
    }
  }

  .mat-mdc-menu-item {
    padding: 0.75rem 1rem !important;
    min-height: auto !important;

    .mat-icon {
      margin-right: 0.75rem;
      color: #667eea;
    }

    &:hover {
      background: #f7fafc !important;
    }
  }
}

.desktop-only {
  display: flex;

  @media (max-width: 768px) {
    display: none !important;
  }
}

.mobile-only {
  display: none !important;

  @media (max-width: 768px) {
    display: flex !important;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 0 1rem !important;
  }

  .header-left .page-title h1 {
    font-size: 1.25rem;
  }

  .header-right {
    gap: 0.5rem;
  }

  .user-menu .user-menu-trigger {
    padding: 0.25rem 0.5rem !important;
    gap: 0.5rem;
  }
}