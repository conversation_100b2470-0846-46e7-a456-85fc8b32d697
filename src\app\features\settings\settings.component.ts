import { Component, OnInit, signal, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatSliderModule } from '@angular/material/slider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDividerModule } from '@angular/material/divider';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';


import { AuthService } from '../../core/services/auth.service';
import { ChurchService } from '@core/services/church.service';
import { Church, UpdateChurchRequest, ServiceTime } from '@shared/models/church.model';
import { NotificationsService } from '../../core/services/notifications.service';

@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatTabsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatSliderModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDialogModule,
    MatTooltipModule,
    MatDividerModule,
    MatDatepickerModule,
    MatNativeDateModule
  ],
  templateUrl: './settings.component.html',
  styleUrl: './settings.component.scss'
})
export class SettingsComponent implements OnInit {
  // Signals for reactive UI
  church = signal<Church | null>(null);
  isLoading = signal<boolean>(false);
  isSaving = signal<boolean>(false);

  // Forms
  churchInfoForm!: FormGroup;
  adminContactForm!: FormGroup;
  locationForm!: FormGroup;
  serviceTimesForm!: FormGroup;

  // Day options for service times
  dayOptions = [
    { value: 'sunday', label: 'Sunday' },
    { value: 'monday', label: 'Monday' },
    { value: 'tuesday', label: 'Tuesday' },
    { value: 'wednesday', label: 'Wednesday' },
    { value: 'thursday', label: 'Thursday' },
    { value: 'friday', label: 'Friday' },
    { value: 'saturday', label: 'Saturday' }
  ];

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private churchService: ChurchService,
    private notificationsService: NotificationsService,
    private snackBar: MatSnackBar
  ) {
    this.initializeForms();
  }

  ngOnInit(): void {
    this.loadChurchData();
  }

  private initializeForms(): void {
    this.churchInfoForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      description: [''],
      phone: [''],
      email: ['', [Validators.email]]
    });

    this.adminContactForm = this.fb.group({
      adminName: ['', [Validators.required, Validators.minLength(2)]],
      adminPhone: ['', [Validators.required]],
      adminEmail: ['', [Validators.required, Validators.email]]
    });

    this.locationForm = this.fb.group({
      address: ['', [Validators.required]],
      latitude: [null, [Validators.required, Validators.min(-90), Validators.max(90)]],
      longitude: [null, [Validators.required, Validators.min(-180), Validators.max(180)]],
      geofenceRadius: [100, [Validators.required, Validators.min(10), Validators.max(1000)]]
    });

    this.serviceTimesForm = this.fb.group({
      serviceTimes: this.fb.array([])
    });
  }

  private loadChurchData(): void {
    const currentUser = this.authService.currentUser();
    console.log('Current user in settings:', currentUser);

    if (!currentUser?.churchId) {
      console.log('No church ID found for user');
      this.showError('No church associated with your account');
      return;
    }

    console.log('Loading church data for ID:', currentUser.churchId);
    this.isLoading.set(true);

    this.churchService.getChurch(currentUser.churchId).subscribe({
      next: (church) => {
        console.log('Church data received:', church);
        this.church.set(church);
        if (church) {
          this.populateForms(church);
        } else {
          console.log('Church data is null');
          this.showError('Church not found');
        }
        this.isLoading.set(false);
      },
      error: (error) => {
        console.error('Error loading church data:', error);
        this.showError('Failed to load church settings');
        this.isLoading.set(false);
      }
    });
  }

  private populateForms(church: Church): void {
    // Populate church info form
    this.churchInfoForm.patchValue({
      name: church.name,
      description: church.description || '',
      phone: church.phone || '',
      email: church.email || ''
    });

    // Populate admin contact form
    this.adminContactForm.patchValue({
      adminName: church.adminName,
      adminPhone: church.adminPhone,
      adminEmail: church.adminEmail
    });

    // Populate location form
    this.locationForm.patchValue({
      address: church.address,
      latitude: church.latitude,
      longitude: church.longitude,
      geofenceRadius: church.geofenceRadius
    });

    // Populate service times form
    this.populateServiceTimes(church.serviceTimes || []);
  }

  private populateServiceTimes(serviceTimes: ServiceTime[]): void {
    const serviceTimesArray = this.serviceTimesForm.get('serviceTimes') as FormArray;
    serviceTimesArray.clear();

    // Ensure serviceTimes is an array
    const serviceTimesData = Array.isArray(serviceTimes) ? serviceTimes : [];
    console.log('Service times data:', serviceTimesData);

    serviceTimesData.forEach(serviceTime => {
      serviceTimesArray.push(this.createServiceTimeFormGroup(serviceTime));
    });

    // Add at least one service time if none exist
    if (serviceTimesData.length === 0) {
      this.addServiceTime();
    }
  }

  private createServiceTimeFormGroup(serviceTime?: ServiceTime): FormGroup {
    // Provide better default values for new service times
    const defaultName = serviceTime?.name || this.getNextServiceName();
    const defaultStartTime = serviceTime?.startTime || '09:00';
    const defaultEndTime = serviceTime?.endTime || '11:00';

    return this.fb.group({
      day: [serviceTime?.day || ''], // Optional - for recurring services
      date: [serviceTime?.date || ''], // Optional - for one-time events
      startTime: [defaultStartTime, [Validators.required]],
      endTime: [defaultEndTime, [Validators.required]],
      name: [defaultName, [Validators.required, Validators.minLength(2)]]
    });
  }

  private getNextServiceName(): string {
    const currentCount = this.serviceTimesArray.length;
    const defaultNames = [
      'Sunday Morning Service',
      'Sunday Evening Service',
      'Wednesday Bible Study',
      'Friday Prayer Meeting',
      'Saturday Youth Service'
    ];

    return defaultNames[currentCount] || `Service ${currentCount + 1}`;
  }

  get serviceTimesArray(): FormArray {
    return this.serviceTimesForm.get('serviceTimes') as FormArray;
  }

  addServiceTime(): void {
    this.serviceTimesArray.push(this.createServiceTimeFormGroup());
  }

  removeServiceTime(index: number): void {
    if (this.serviceTimesArray.length > 1) {
      this.serviceTimesArray.removeAt(index);
    }
  }



  onSaveChurchInfo(): void {
    if (this.churchInfoForm.valid && this.church()) {
      this.isSaving.set(true);
      const formData = this.churchInfoForm.value;

      console.log('Saving church info:', formData);
      console.log('Church ID:', this.church()!.id);

      this.churchService.updateChurch(this.church()!.id, formData).subscribe({
        next: (response) => {
          console.log('Church info updated successfully:', response);
          this.showSuccess('Church information updated successfully');
          this.isSaving.set(false);
        },
        error: (error) => {
          console.error('Failed to update church information:', error);
          let errorMessage = 'Failed to update church information';
          if (error.error?.message) {
            errorMessage = error.error.message;
          } else if (error.message) {
            errorMessage = error.message;
          }
          this.showError(errorMessage);
          this.isSaving.set(false);
        }
      });
    } else {
      console.log('Form invalid or no church data');
      console.log('Form errors:', this.churchInfoForm.errors);
      console.log('Form value:', this.churchInfoForm.value);
      console.log('Church data:', this.church());
    }
  }

  onSaveAdminContact(): void {
    if (this.adminContactForm.valid && this.church()) {
      this.isSaving.set(true);
      const formData = this.adminContactForm.value;

      this.churchService.updateChurch(this.church()!.id, formData).subscribe({
        next: (response) => {
          this.showSuccess('Admin contact information updated successfully');
          this.isSaving.set(false);
        },
        error: (error) => {
          this.showError('Failed to update admin contact information');
          this.isSaving.set(false);
        }
      });
    }
  }

  onSaveLocation(): void {
    if (this.locationForm.valid && this.church()) {
      this.isSaving.set(true);
      const rawFormData = this.locationForm.value;

      // Ensure proper data types for API
      const formData = {
        address: rawFormData.address,
        latitude: Number(rawFormData.latitude),
        longitude: Number(rawFormData.longitude),
        geofenceRadius: Number(rawFormData.geofenceRadius)
      };

      console.log('Saving location data:', formData);
      console.log('Church ID:', this.church()!.id);

      this.churchService.updateChurch(this.church()!.id, formData).subscribe({
        next: () => {
          console.log('Location updated successfully');
          this.showSuccess('Location settings updated successfully');
          this.isSaving.set(false);
        },
        error: (error) => {
          console.error('Failed to update location settings:', error);
          let errorMessage = 'Failed to update location settings';
          if (error.error?.message) {
            errorMessage = error.error.message;
          } else if (error.message) {
            errorMessage = error.message;
          }
          this.showError(errorMessage);
          this.isSaving.set(false);
        }
      });
    } else {
      console.log('Form invalid or no church data');
      console.log('Form errors:', this.locationForm.errors);
      console.log('Form value:', this.locationForm.value);
      console.log('Church data:', this.church());
    }
  }

  onSaveServiceTimes(): void {
    if (this.serviceTimesForm.valid && this.church()) {
      this.isSaving.set(true);
      const serviceTimes = this.serviceTimesForm.value.serviceTimes;

      this.churchService.updateChurch(this.church()!.id, { serviceTimes }).subscribe({
        next: () => {
          this.showSuccess('Service times updated successfully');

          // Send notification to church members about the service schedule update
          this.notificationsService.sendServiceTimesUpdateNotification(serviceTimes).subscribe({
            next: (notificationResult) => {
              console.log('Service schedule notification sent:', notificationResult);
              this.showSuccess(`Notification sent to ${notificationResult.result.successCount} members`);
            },
            error: (notificationError) => {
              console.error('Failed to send notification:', notificationError);
              // Don't show error to user as the main action (updating service times) was successful
            }
          });

          this.isSaving.set(false);
        },
        error: (error) => {
          this.showError('Failed to update service times');
          this.isSaving.set(false);
        }
      });
    }
  }

  onResetForm(formName: string): void {
    const church = this.church();
    if (!church) return;

    switch (formName) {
      case 'churchInfo':
        this.churchInfoForm.patchValue({
          name: church.name,
          description: church.description || '',
          phone: church.phone || '',
          email: church.email || ''
        });
        break;
      case 'adminContact':
        this.adminContactForm.patchValue({
          adminName: church.adminName,
          adminPhone: church.adminPhone,
          adminEmail: church.adminEmail
        });
        break;
      case 'location':
        this.locationForm.patchValue({
          address: church.address,
          latitude: church.latitude,
          longitude: church.longitude,
          geofenceRadius: church.geofenceRadius
        });
        break;
      case 'serviceTimes':
        this.populateServiceTimes(church.serviceTimes || []);
        break;
    }
  }

  onRefreshData(): void {
    this.loadChurchData();
  }



  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }



  formatGeofenceRadius(value: number): string {
    return `${value}m`;
  }

  getDayLabel(day: string): string {
    const dayOption = this.dayOptions.find(option => option.value === day);
    return dayOption ? dayOption.label : day;
  }
}
