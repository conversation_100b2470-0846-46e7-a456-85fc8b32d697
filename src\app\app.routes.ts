import { Routes } from '@angular/router';
import { authGuard, adminGuard, guestGuard, superAdminGuard } from '@core/guards/auth.guard';

export const routes: Routes = [
    {
        path: '',
        loadComponent: () => import('@features/landing/landing.component').then(m => m.LandingComponent)
    },
    {
        path: 'auth/login',
        canActivate: [guestGuard],
        loadComponent: () => import('@features/auth/components/login/login.component').then(m => m.LoginComponent)
    },
    {
        path: 'auth/signup',
        canActivate: [guestGuard],
        loadComponent: () => import('@features/auth/components/signup/signup.component').then(m => m.SignupComponent)
    },
    {
        path: 'auth/church-registration',
        canActivate: [guestGuard],
        loadComponent: () => import('@features/auth/components/church-registration/church-registration.component').then(m => m.ChurchRegistrationComponent)
    },
    {
        path: 'login',
        redirectTo: '/auth/login',
        pathMatch: 'full'
    },
    {
        path: '',
        canActivate: [authGuard],
        loadComponent: () => import('@app/layout/dashboard-layout/dashboard-layout.component').then(m => m.DashboardLayoutComponent),
        children: [
            {
                path: 'dashboard',
                loadComponent: () => import('@features/dashboard/dashboard.component').then(m => m.DashboardComponent)
            },
            {
                path: 'attendance/live',
                canActivate: [adminGuard],
                loadComponent: () => import('@features/attendance/live-attendance/live-attendance.component').then(m => m.LiveAttendanceComponent)
            },
            {
                path: 'reports',
                canActivate: [adminGuard],
                loadComponent: () => import('@features/reports/reports.component').then(m => m.ReportsComponent)
            },
            {
                path: 'members',
                canActivate: [adminGuard],
                loadComponent: () => import('@features/members/members.component').then(m => m.MembersComponent)
            },
            {
                path: 'members/bulk-import',
                canActivate: [adminGuard],
                loadComponent: () => import('@features/members/bulk-import/bulk-import.component').then(m => m.BulkImportComponent)
            },
            {
                path: 'settings',
                canActivate: [adminGuard],
                loadComponent: () => import('@features/settings/settings.component').then(m => m.SettingsComponent)
            },
            {
                path: 'notifications',
                canActivate: [adminGuard],
                loadComponent: () => import('@features/notifications/notifications.component').then(m => m.NotificationsComponent)
            },
            {
                path: 'super-admin',
                canActivate: [superAdminGuard],
                children: [
                    {
                        path: 'churches',
                        loadComponent: () => import('@features/super-admin/churches/super-admin-churches.component').then(m => m.SuperAdminChurchesComponent)
                    },
                    {
                        path: 'users',
                        loadComponent: () => import('@features/super-admin/users/super-admin-users.component').then(m => m.SuperAdminUsersComponent)
                    },
                    {
                        path: 'reports',
                        loadComponent: () => import('@features/super-admin/reports/super-admin-reports.component').then(m => m.SuperAdminReportsComponent)
                    },
                    {
                        path: 'settings',
                        loadComponent: () => import('@features/super-admin/settings/super-admin-settings.component').then(m => m.SuperAdminSettingsComponent)
                    },
                    {
                        path: '',
                        redirectTo: 'churches',
                        pathMatch: 'full'
                    }
                ]
            }
        ]
    },
    {
        path: '**',
        redirectTo: '/dashboard'
    }
];
