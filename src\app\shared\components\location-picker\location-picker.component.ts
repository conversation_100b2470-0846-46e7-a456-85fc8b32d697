import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ViewChild, ElementRef, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import * as L from 'leaflet';

export interface LocationCoordinates {
  latitude: number;
  longitude: number;
  address?: string;
}

@Component({
  selector: 'app-location-picker',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ],
  templateUrl: './location-picker.component.html',
  styleUrl: './location-picker.component.scss'
})
export class LocationPickerComponent implements OnInit, OnDestroy {
  @Input() initialLocation?: LocationCoordinates;
  @Output() locationSelected = new EventEmitter<LocationCoordinates>();

  @ViewChild('mapContainer', { static: true }) mapContainer!: ElementRef;

  // Signals
  selectedLocation = signal<LocationCoordinates | null>(null);
  isSearching = signal<boolean>(false);

  // Form data
  searchAddress = '';
  manualLatitude: number | null = null;
  manualLongitude: number | null = null;

  // Map instance
  private map: L.Map | null = null;
  private marker: L.Marker | null = null;

  constructor(private snackBar: MatSnackBar) { }

  ngOnInit(): void {
    this.initializeMap();

    if (this.initialLocation) {
      this.setLocation(this.initialLocation);
    }
  }

  ngOnDestroy(): void {
    if (this.map) {
      this.map.remove();
    }
  }

  private initializeMap(): void {
    // Default to Accra, Ghana
    const defaultLat = 5.6037;
    const defaultLng = -0.1870;

    this.map = L.map(this.mapContainer.nativeElement).setView([defaultLat, defaultLng], 13);

    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors'
    }).addTo(this.map);

    // Add click handler
    this.map.on('click', (e: L.LeafletMouseEvent) => {
      this.setLocationFromMap(e.latlng.lat, e.latlng.lng);
    });
  }

  private setLocationFromMap(lat: number, lng: number): void {
    const location: LocationCoordinates = {
      latitude: lat,
      longitude: lng
    };

    this.setLocation(location);
    this.locationSelected.emit(location);
  }

  private setLocation(location: LocationCoordinates): void {
    this.selectedLocation.set(location);
    this.manualLatitude = location.latitude;
    this.manualLongitude = location.longitude;

    if (this.map) {
      // Remove existing marker
      if (this.marker) {
        this.map.removeLayer(this.marker);
      }

      // Add new marker
      this.marker = L.marker([location.latitude, location.longitude]).addTo(this.map);

      // Center map on location
      this.map.setView([location.latitude, location.longitude], 15);
    }
  }

  async onSearchAddress(): Promise<void> {
    if (!this.searchAddress.trim()) {
      this.snackBar.open('Please enter an address to search', 'Close', { duration: 3000 });
      return;
    }

    this.isSearching.set(true);

    try {
      // Use Nominatim API for geocoding (free OpenStreetMap service)
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(this.searchAddress)}&limit=1`
      );

      const results = await response.json();

      if (results && results.length > 0) {
        const result = results[0];
        const location: LocationCoordinates = {
          latitude: parseFloat(result.lat),
          longitude: parseFloat(result.lon),
          address: result.display_name
        };

        this.setLocation(location);
        this.locationSelected.emit(location);
        this.snackBar.open('Location found!', 'Close', { duration: 3000 });
      } else {
        this.snackBar.open('Address not found. Please try a different search.', 'Close', { duration: 5000 });
      }
    } catch (error) {
      console.error('Geocoding error:', error);
      this.snackBar.open('Error searching for address. Please try again.', 'Close', { duration: 5000 });
    } finally {
      this.isSearching.set(false);
    }
  }

  onManualCoordinateChange(): void {
    if (this.manualLatitude !== null && this.manualLongitude !== null) {
      // Validate coordinates
      if (this.manualLatitude >= -90 && this.manualLatitude <= 90 &&
        this.manualLongitude >= -180 && this.manualLongitude <= 180) {

        const location: LocationCoordinates = {
          latitude: this.manualLatitude,
          longitude: this.manualLongitude
        };

        this.setLocation(location);
        this.locationSelected.emit(location);
      }
    }
  }
}
