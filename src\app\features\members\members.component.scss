.members-container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  min-height: calc(100vh - 4rem);
}

// Header Section
.members-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;

  .header-content {
    flex: 1;

    .page-title {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      font-size: 2rem;
      font-weight: 700;
      color: #1a202c;
      margin: 0 0 0.5rem 0;

      mat-icon {
        font-size: 2rem;
        width: 2rem;
        height: 2rem;
        color: #667eea;
      }
    }

    .page-subtitle {
      color: #718096;
      font-size: 1rem;
      margin: 0;
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;

    .add-member-btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1.5rem;
      font-weight: 600;
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;

    .header-actions {
      justify-content: flex-end;
    }
  }
}

// Stats Grid
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;

  .stat-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .stat-content {
      display: flex;
      align-items: center;
      gap: 1rem;

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;

        mat-icon {
          font-size: 24px;
          width: 24px;
          height: 24px;
          color: white;
        }

        &.total {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.active {
          background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
        }

        &.new {
          background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
        }

        &.recent {
          background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        }
      }

      .stat-info {
        h3 {
          font-size: 1.75rem;
          font-weight: 700;
          color: #1a202c;
          margin: 0;
        }

        p {
          font-size: 0.875rem;
          color: #718096;
          margin: 0.25rem 0 0 0;
        }
      }
    }
  }
}

// Filters Card
.filters-card {
  margin-bottom: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .filters-content {
    display: flex;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;

    .search-field {
      flex: 1;
      min-width: 300px;
    }

    .filter-options {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
      gap: 1rem;

      .search-field {
        min-width: unset;
      }
    }
  }
}

// Table Card
.table-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    gap: 1rem;

    p {
      color: #718096;
      margin: 0;
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    text-align: center;

    mat-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      color: #cbd5e0;
      margin-bottom: 1rem;
    }

    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: #4a5568;
      margin: 0 0 0.5rem 0;
    }

    p {
      color: #718096;
      margin: 0;
    }
  }

  .table-container {
    overflow-x: auto;

    .members-table {
      width: 100%;
      min-width: 800px;

      .mat-mdc-header-cell {
        font-weight: 600;
        color: #4a5568;
        border-bottom: 2px solid #e2e8f0;
      }

      .mat-mdc-cell {
        border-bottom: 1px solid #f7fafc;
        padding: 1rem 0.75rem;
      }

      .mat-mdc-row {
        transition: background-color 0.2s ease;

        &:hover {
          background-color: #f8fafc;
        }
      }

      // Member info styling
      .member-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;

        .member-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          object-fit: cover;
        }

        .member-avatar-placeholder {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 0.875rem;
        }

        .member-details {
          .member-name {
            font-weight: 500;
            color: #1a202c;
          }
        }
      }

      // Action menu styling
      .delete-action {
        color: #e53e3e;

        mat-icon {
          color: #e53e3e;
        }
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .members-container {
    padding: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .table-card .table-container .members-table {
    min-width: 600px;
  }
}

// Material Design overrides
::ng-deep {
  .mat-mdc-chip {
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .mat-mdc-menu-panel {
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  .mat-mdc-card {
    border-radius: 12px;
  }
}
