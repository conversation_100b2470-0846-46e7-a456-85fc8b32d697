import { Component, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, AbstractControl } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { firstValueFrom } from 'rxjs';
import { AuthService } from '@core/services/auth.service';
import { ChurchService } from '@core/services/church.service';
import { Church } from '@shared/models/church.model';
import { CreateUserRequest } from '@shared/models/user.model';

@Component({
  selector: 'app-signup',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterModule],
  templateUrl: './signup.component.html',
  styleUrls: ['./signup.component.scss']
})
export class SignupComponent {
  signupForm: FormGroup;
  isLoading = signal(false);
  error = signal<string | null>(null);
  churches = signal<Church[]>([]);
  isLoadingChurches = signal(false);

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private churchService: ChurchService,
    private router: Router
  ) {
    this.signupForm = this.fb.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(8)]],
      confirmPassword: ['', [Validators.required]],
      churchId: ['', [Validators.required]],
      role: ['member', [Validators.required]]
    }, { validators: this.passwordMatchValidator });

    this.loadChurches();
  }

  passwordMatchValidator(control: AbstractControl): { [key: string]: boolean } | null {
    const password = control.get('password');
    const confirmPassword = control.get('confirmPassword');

    if (!password || !confirmPassword) {
      return null;
    }

    return password.value === confirmPassword.value ? null : { passwordMismatch: true };
  }

  async loadChurches(): Promise<void> {
    this.isLoadingChurches.set(true);
    try {
      const churchList = await firstValueFrom(this.churchService.getChurches());
      this.churches.set(churchList || []);
    } catch (error) {
      console.error('Failed to load churches:', error);
      this.error.set('Failed to load churches. Please refresh the page.');
    } finally {
      this.isLoadingChurches.set(false);
    }
  }

  async onSubmit(): Promise<void> {
    if (this.signupForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.isLoading.set(true);
    this.error.set(null);

    try {
      const formValue = this.signupForm.value;
      const userData: CreateUserRequest = {
        firstName: formValue.firstName,
        lastName: formValue.lastName,
        email: formValue.email,
        churchId: formValue.churchId,
        role: formValue.role as 'super_admin' | 'admin' | 'pastor' | 'member'
      };

      await this.authService.signUp(userData, formValue.password);

      // Registration successful, redirect to dashboard
      this.router.navigate(['/dashboard']);
    } catch (error: any) {
      this.error.set(error.message || 'Registration failed. Please try again.');
    } finally {
      this.isLoading.set(false);
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.signupForm.controls).forEach(key => {
      const control = this.signupForm.get(key);
      control?.markAsTouched();
    });
  }

  getFieldError(fieldName: string): string | null {
    const field = this.signupForm.get(fieldName);
    if (field?.touched && field?.errors) {
      if (field.errors['required']) {
        return `${this.getFieldDisplayName(fieldName)} is required`;
      }
      if (field.errors['email']) {
        return 'Please enter a valid email address';
      }
      if (field.errors['minlength']) {
        const requiredLength = field.errors['minlength'].requiredLength;
        return `${this.getFieldDisplayName(fieldName)} must be at least ${requiredLength} characters`;
      }
      if (field.errors['pattern']) {
        return 'Please enter a valid format';
      }
    }

    if (fieldName === 'confirmPassword' && this.signupForm.errors?.['passwordMismatch'] && field?.touched) {
      return 'Passwords do not match';
    }

    return null;
  }

  private getFieldDisplayName(fieldName: string): string {
    const displayNames: { [key: string]: string } = {
      firstName: 'First name',
      lastName: 'Last name',
      email: 'Email',
      password: 'Password',
      confirmPassword: 'Confirm password',
      churchId: 'Church',
      role: 'Role'
    };
    return displayNames[fieldName] || fieldName;
  }

  get appName(): string {
    return 'Flockin Dashboard';
  }
}
