<div class="auth-container">
  <!-- Left Section - Login Form -->
  <div class="auth-form-section">
    <div class="form-container">
      <div class="form-header">
        <h1>Welcome Back</h1>
        <p class="subtitle">Sign in to access your church dashboard</p>
      </div>

      @if (successMessage()) {
      <div class="success-message">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" />
        </svg>
        {{ successMessage() }}
      </div>
      }

      @if (error()) {
      <div class="error-message">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path
            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
        </svg>
        {{ error() }}
      </div>
      }

      <form [formGroup]="loginForm" (ngSubmit)="onLogin()" class="login-form">
        <div class="form-group">
          <label for="email">Email Address</label>
          <input type="email" id="email" formControlName="email" placeholder="Enter your email address"
            [class.error]="getFieldError('email')" />
          @if (getFieldError('email')) {
          <span class="field-error">{{ getFieldError('email') }}</span>
          }
        </div>

        <div class="form-group">
          <label for="password">Password</label>
          <input type="password" id="password" formControlName="password" placeholder="Enter your password"
            [class.error]="getFieldError('password')" />
          @if (getFieldError('password')) {
          <span class="field-error">{{ getFieldError('password') }}</span>
          }
        </div>

        <button type="submit" class="login-btn" [disabled]="isLoading() || loginForm.invalid">
          @if (isLoading()) {
          <div class="loading-spinner"></div>
          <span>Signing in...</span>
          } @else {
          <span>Sign In</span>
          }
        </button>
      </form>

      <div class="signup-link">
        <p>Don't have an account? <a routerLink="/auth/church-registration">Register your church</a></p>
      </div>
    </div>
  </div>

  <!-- Right Section - Content -->
  <div class="auth-content-section">
    <div class="content-container">
      <div class="brand-section">
        <div class="logo">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor" class="logo-icon">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
          </svg>
          <h1>{{ appName }}</h1>
        </div>
        <p class="tagline">Modern Church Attendance Management</p>
      </div>

      <div class="features-section">
        <h2>Streamline Your Church Operations</h2>
        <div class="features-list">
          <div class="feature-item">
            <div class="feature-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A1.5 1.5 0 0 0 18.54 8H16c-.8 0-1.54.37-2.01 1l-2.99 4v7h2v7h4v-7h2v7h4z" />
              </svg>
            </div>
            <div class="feature-content">
              <h3>Real-time Attendance</h3>
              <p>Track member attendance instantly with digital check-ins</p>
            </div>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z" />
              </svg>
            </div>
            <div class="feature-content">
              <h3>Detailed Reports</h3>
              <p>Generate comprehensive attendance reports and insights</p>
            </div>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm-1 16H9V7h9v14z" />
              </svg>
            </div>
            <div class="feature-content">
              <h3>Member Management</h3>
              <p>Easily manage your congregation and track engagement</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>