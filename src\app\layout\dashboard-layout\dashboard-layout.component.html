<div class="dashboard-layout">
  <!-- Mobile Menu Overlay -->
  @if (isMobileMenuOpen()) {
    <div class="mobile-overlay" (click)="onCloseMobileMenu()"></div>
  }

  <!-- Sidebar -->
  <app-sidebar 
    [isCollapsed]="isSidebarCollapsed()"
    [isMobileOpen]="isMobileMenuOpen()"
    (closeMobileMenu)="onCloseMobileMenu()">
  </app-sidebar>

  <!-- Main Content Area -->
  <div class="main-content" [class.sidebar-collapsed]="isSidebarCollapsed()">
    <!-- Header -->
    <app-header 
      [currentUser]="currentUser()"
      (toggleSidebar)="onToggleSidebar()"
      (toggleMobileMenu)="onToggleMobileMenu()">
    </app-header>

    <!-- Page Content -->
    <main class="page-content">
      <router-outlet></router-outlet>
    </main>
  </div>
</div>
