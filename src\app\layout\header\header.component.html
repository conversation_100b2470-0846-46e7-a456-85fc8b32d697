<mat-toolbar class="header">
  <div class="header-left">
    <!-- Desktop Sidebar Toggle -->
    <button mat-icon-button class="sidebar-toggle desktop-only" (click)="onToggleSidebar()" aria-label="Toggle sidebar">
      <mat-icon>menu</mat-icon>
    </button>

    <!-- Mobile Menu Toggle -->
    <button mat-icon-button class="mobile-menu-toggle mobile-only" (click)="onToggleMobileMenu()"
      aria-label="Toggle mobile menu">
      <mat-icon>menu</mat-icon>
    </button>

    <!-- Page Title -->
    <div class="page-title">
      <h1>Dashboard</h1>
    </div>
  </div>

  <div class="header-right">
    <!-- Notifications -->
    <button mat-icon-button class="notification-btn" aria-label="View notifications" matBadge="3" matBadgeColor="accent"
      matBadgeSize="small">
      <mat-icon>notifications</mat-icon>
    </button>

    <!-- User Menu -->
    <div class="user-menu">
      <button mat-button class="user-menu-trigger" [matMenuTriggerFor]="userMenu" aria-label="User menu">
        @if (currentUser?.profilePicture) {
        <img [src]="currentUser?.profilePicture" [alt]="getUserDisplayName(currentUser)" class="user-avatar">
        }
        <div class="user-info">
          <span class="user-name">{{ getUserDisplayName(currentUser) }}</span>
          <span class="user-role">{{ currentUser?.role }}</span>
        </div>
        <mat-icon class="dropdown-arrow">expand_more</mat-icon>
      </button>

      <!-- Material Menu -->
      <mat-menu #userMenu="matMenu" class="user-dropdown">
        <div class="dropdown-header" mat-menu-item disabled>
          <div class="user-details">
            <div class="user-name">{{ getUserDisplayName(currentUser) }}</div>
            <div class="user-email">{{ currentUser?.email }}</div>
          </div>
        </div>

        <mat-divider></mat-divider>

        <button mat-menu-item (click)="onProfile()">
          <mat-icon>person</mat-icon>
          <span>Profile</span>
        </button>

        <button mat-menu-item (click)="onSettings()">
          <mat-icon>settings</mat-icon>
          <span>Settings</span>
        </button>

        <mat-divider></mat-divider>

        <button mat-menu-item (click)="onSignOut()">
          <mat-icon>logout</mat-icon>
          <span>Sign Out</span>
        </button>
      </mat-menu>
    </div>
  </div>
</mat-toolbar>