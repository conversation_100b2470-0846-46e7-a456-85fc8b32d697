{"name": "flockin_frontend", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "prettier": {"overrides": [{"files": "*.html", "options": {"parser": "angular"}}]}, "private": true, "dependencies": {"@angular/animations": "^20.1.0", "@angular/cdk": "^20.1.0", "@angular/common": "^20.1.0", "@angular/compiler": "^20.1.0", "@angular/core": "^20.1.0", "@angular/fire": "^20.0.1", "@angular/forms": "^20.1.0", "@angular/material": "^20.1.0", "@angular/platform-browser": "^20.1.0", "@angular/router": "^20.1.0", "@auth0/angular-jwt": "^5.2.0", "@types/papaparse": "^5.3.16", "chart.js": "^4.5.0", "firebase": "^12.0.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "leaflet": "^1.9.4", "ng2-charts": "^8.0.0", "papaparse": "^5.5.3", "rxjs": "~7.8.0", "tslib": "^2.3.0"}, "devDependencies": {"@angular/build": "^20.1.0", "@angular/cli": "^20.1.0", "@angular/compiler-cli": "^20.1.0", "@types/jasmine": "~5.1.0", "@types/leaflet": "^1.9.20", "firebase-tools": "^14.11.1", "jasmine-core": "~5.8.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.2"}}