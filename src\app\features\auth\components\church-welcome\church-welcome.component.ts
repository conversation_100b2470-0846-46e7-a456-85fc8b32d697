import { Component, signal, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { AuthService } from '@core/services/auth.service';
import { ChurchService } from '@core/services/church.service';
import { User } from '@shared/models/user.model';
import { Church } from '@shared/models/church.model';

@Component({
  selector: 'app-church-welcome',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './church-welcome.component.html',
  styleUrls: ['./church-welcome.component.scss']
})
export class ChurchWelcomeComponent implements OnInit {
  user = signal<User | null>(null);
  church = signal<Church | null>(null);
  isLoading = signal(true);
  currentStep = signal(1);
  totalSteps = 4;

  constructor(
    private authService: AuthService,
    private churchService: ChurchService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.loadUserAndChurch();
  }

  private async loadUserAndChurch(): Promise<void> {
    try {
      const currentUser = this.authService.currentUser();
      if (currentUser) {
        this.user.set(currentUser);

        if (currentUser.churchId) {
          this.churchService.getChurch(currentUser.churchId).subscribe({
            next: (church) => {
              this.church.set(church);
              this.isLoading.set(false);
            },
            error: (error) => {
              console.error('Error loading church:', error);
              this.isLoading.set(false);
            }
          });
        } else {
          this.isLoading.set(false);
        }
      } else {
        this.router.navigate(['/auth/login']);
      }
    } catch (error) {
      console.error('Error loading user data:', error);
      this.isLoading.set(false);
    }
  }

  nextStep(): void {
    if (this.currentStep() < this.totalSteps) {
      this.currentStep.set(this.currentStep() + 1);
    }
  }

  previousStep(): void {
    if (this.currentStep() > 1) {
      this.currentStep.set(this.currentStep() - 1);
    }
  }

  goToDashboard(): void {
    this.router.navigate(['/dashboard']);
  }

  goToSettings(): void {
    this.router.navigate(['/settings']);
  }

  goToMembers(): void {
    this.router.navigate(['/members']);
  }

  get welcomeSteps() {
    return [
      {
        title: 'Welcome to Flockin!',
        description: 'Your church has been successfully registered and you\'re now ready to start managing attendance.',
        icon: '🎉'
      },
      {
        title: 'Invite Your Members',
        description: 'Start by inviting your church members to join the platform. They can register using your church code.',
        icon: '👥'
      },
      {
        title: 'Configure Settings',
        description: 'Customize your church settings, service times, and attendance tracking preferences.',
        icon: '⚙️'
      },
      {
        title: 'Start Tracking',
        description: 'You\'re all set! Members can now check in to services and you can track attendance in real-time.',
        icon: '📊'
      }
    ];
  }

  get currentStepData() {
    return this.welcomeSteps[this.currentStep() - 1];
  }

  get progressPercentage(): number {
    return (this.currentStep() / this.totalSteps) * 100;
  }
}
