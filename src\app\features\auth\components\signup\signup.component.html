<div class="auth-container">
  <!-- Left Section - Registration Form -->
  <div class="auth-form-section">
    <div class="form-container">
      <div class="form-header">
        <h1>Join Your Church</h1>
        <p class="subtitle">Create your member account to join your church community</p>
      </div>

      @if (error()) {
      <div class="error-message">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path
            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
        </svg>
        {{ error() }}
      </div>
      }

      <form [formGroup]="signupForm" (ngSubmit)="onSubmit()" class="signup-form">
        <div class="form-row">
          <div class="form-group">
            <label for="firstName">First Name</label>
            <input type="text" id="firstName" formControlName="firstName" placeholder="Enter your first name"
              [class.error]="getFieldError('firstName')" />
            @if (getFieldError('firstName')) {
            <span class="field-error">{{ getFieldError('firstName') }}</span>
            }
          </div>

          <div class="form-group">
            <label for="lastName">Last Name</label>
            <input type="text" id="lastName" formControlName="lastName" placeholder="Enter your last name"
              [class.error]="getFieldError('lastName')" />
            @if (getFieldError('lastName')) {
            <span class="field-error">{{ getFieldError('lastName') }}</span>
            }
          </div>
        </div>

        <div class="form-group">
          <label for="email">Email Address</label>
          <input type="email" id="email" formControlName="email" placeholder="Enter your email address"
            [class.error]="getFieldError('email')" />
          @if (getFieldError('email')) {
          <span class="field-error">{{ getFieldError('email') }}</span>
          }
        </div>

        <div class="form-group">
          <label for="phone">Phone Number</label>
          <input type="tel" id="phone" formControlName="phone" placeholder="Enter your phone number (e.g., ******-0123)"
            [class.error]="getFieldError('phone')" />
          @if (getFieldError('phone')) {
          <span class="field-error">{{ getFieldError('phone') }}</span>
          }
        </div>

        <div class="form-group">
          <label for="churchId">Select Your Church</label>
          @if (isLoadingChurches()) {
          <div class="loading-select">Loading churches...</div>
          } @else {
          <select id="churchId" formControlName="churchId" [class.error]="getFieldError('churchId')">
            <option value="">Choose your church</option>
            @for (church of churches(); track church.id) {
            <option [value]="church.id">{{ church.name }}</option>
            }
          </select>
          }
          @if (getFieldError('churchId')) {
          <span class="field-error">{{ getFieldError('churchId') }}</span>
          }
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="password">Password</label>
            <input type="password" id="password" formControlName="password"
              placeholder="Create a password (min. 8 characters)" [class.error]="getFieldError('password')" />
            @if (getFieldError('password')) {
            <span class="field-error">{{ getFieldError('password') }}</span>
            }
          </div>

          <div class="form-group">
            <label for="confirmPassword">Confirm Password</label>
            <input type="password" id="confirmPassword" formControlName="confirmPassword"
              placeholder="Confirm your password" [class.error]="getFieldError('confirmPassword')" />
            @if (getFieldError('confirmPassword')) {
            <span class="field-error">{{ getFieldError('confirmPassword') }}</span>
            }
          </div>
        </div>

        <button type="submit" class="signup-btn" [disabled]="isLoading() || signupForm.invalid">
          @if (isLoading()) {
          <div class="loading-spinner"></div>
          <span>Creating Account...</span>
          } @else {
          <span>Create Account</span>
          }
        </button>
      </form>

      <div class="login-link">
        <p>Already have an account? <a routerLink="/auth/login">Sign in here</a></p>
        <p>Are you a church administrator? <a routerLink="/auth/church-registration">Register your church</a></p>
      </div>
    </div>
  </div>

  <!-- Right Section - Content -->
  <div class="auth-content-section">
    <div class="content-container">
      <div class="brand-section">
        <div class="logo">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor" class="logo-icon">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
          </svg>
          <h1>{{ appName }}</h1>
        </div>
        <p class="tagline">Modern Church Attendance Management</p>
      </div>

      <div class="features-section">
        <h2>Join Your Church Community</h2>
        <div class="features-list">
          <div class="feature-item">
            <div class="feature-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
              </svg>
            </div>
            <div class="feature-content">
              <h3>Connect with Members</h3>
              <p>Build stronger relationships within your church community</p>
            </div>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-9H19v2h-1.5v17.5c0 .83-.67 1.5-1.5 1.5h-9c-.83 0-1.5-.67-1.5-1.5V4H4V2h4.5l1-1h5l1 1H19z" />
              </svg>
            </div>
            <div class="feature-content">
              <h3>Track Your Attendance</h3>
              <p>Keep track of your church participation and engagement</p>
            </div>
          </div>

          <div class="feature-item">
            <div class="feature-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path
                  d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
              </svg>
            </div>
            <div class="feature-content">
              <h3>Stay Informed</h3>
              <p>Get updates about church events and activities</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>