.reports-container {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
  background: #f7fafc;
  min-height: 100vh;
}

// Header Section
.reports-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  color: white;

  .header-content {
    h1 {
      font-size: 2rem;
      font-weight: 700;
      margin: 0 0 0.5rem 0;
    }

    p {
      font-size: 1rem;
      opacity: 0.9;
      margin: 0;
    }
  }

  .header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
  }

  .refresh-btn,
  .export-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-1px);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    svg {
      width: 16px;
      height: 16px;
    }
  }


}

// Error Message
.error-message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  background: #fed7d7;
  border: 1px solid #feb2b2;
  border-radius: 8px;
  color: #c53030;
  margin-bottom: 1.5rem;

  svg {
    flex-shrink: 0;
  }

  .retry-btn {
    margin-left: auto;
    padding: 0.5rem 1rem;
    background: #c53030;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;

    &:hover {
      background: #9c2626;
    }
  }
}

// Controls Section
.reports-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  align-items: flex-end;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;

  .control-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    label {
      font-weight: 600;
      color: #2d3748;
      font-size: 0.875rem;
    }
  }

  .report-type-tabs {
    display: flex;
    gap: 0.5rem;

    .tab-btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1rem;
      background: #f7fafc;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      color: #4a5568;
      cursor: pointer;
      transition: all 0.2s ease;
      font-weight: 500;

      &:hover {
        background: #edf2f7;
        border-color: #cbd5e0;
      }

      &.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: #667eea;
        color: white;
      }

      svg {
        width: 16px;
        height: 16px;
      }
    }
  }

  .date-range-select {
    padding: 0.75rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #2d3748;
    font-size: 0.875rem;
    min-width: 150px;

    &:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
  }

  .custom-date-range {
    display: flex;
    gap: 1rem;

    .date-input-group {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      .date-input {
        padding: 0.75rem 1rem;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        background: white;
        color: #2d3748;
        font-size: 0.875rem;

        &:focus {
          outline: none;
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
      }
    }
  }

  .advanced-filters-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    color: #4a5568;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;

    &:hover {
      background: #edf2f7;
      border-color: #cbd5e0;
    }

    svg:last-child {
      transition: transform 0.2s ease;

      &.rotated {
        transform: rotate(180deg);
      }
    }
  }
}

// Advanced Filters Panel
.advanced-filters-panel {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
  overflow: hidden;

  .filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    background: #f7fafc;

    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: #2d3748;
      margin: 0;
    }

    .reset-filters-btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      background: #e53e3e;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-weight: 500;
      transition: background-color 0.2s ease;

      &:hover {
        background: #c53030;
      }

      svg {
        width: 14px;
        height: 14px;
      }
    }
  }

  .filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;

    .filter-group {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      label {
        font-weight: 600;
        color: #2d3748;
        font-size: 0.875rem;

        &.checkbox-label {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          cursor: pointer;
          font-weight: 500;

          input[type="checkbox"] {
            display: none;
          }

          .checkbox-custom {
            width: 18px;
            height: 18px;
            border: 2px solid #e2e8f0;
            border-radius: 4px;
            position: relative;
            transition: all 0.2s ease;

            &::after {
              content: '';
              position: absolute;
              top: 2px;
              left: 6px;
              width: 4px;
              height: 8px;
              border: solid white;
              border-width: 0 2px 2px 0;
              transform: rotate(45deg);
              opacity: 0;
              transition: opacity 0.2s ease;
            }
          }

          input[type="checkbox"]:checked+.checkbox-custom {
            background: #667eea;
            border-color: #667eea;

            &::after {
              opacity: 1;
            }
          }
        }
      }

      .filter-select,
      .filter-input {
        padding: 0.75rem 1rem;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        background: white;
        color: #2d3748;
        font-size: 0.875rem;

        &:focus {
          outline: none;
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
      }

      .filter-input {
        &[type="number"] {
          -moz-appearance: textfield;

          &::-webkit-outer-spin-button,
          &::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    .filters-header {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;
    }

    .filters-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
  }
}

// Loading State
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #e2e8f0;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  p {
    color: #718096;
    font-size: 1rem;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// Report Content
.report-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

// Charts Section
.charts-section {
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  background: #f7fafc;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

.report-summary {
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;

  h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin: 0 0 1rem 0;
  }

  .summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;

    .stat-card {
      padding: 1rem;
      background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
      border-radius: 8px;
      text-align: center;

      .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 0.25rem;
      }

      .stat-label {
        font-size: 0.875rem;
        color: #718096;
        font-weight: 500;
      }
    }
  }
}

// Table Styles
.table-container {
  overflow-x: auto;
  padding: 1.5rem;

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 1rem 0;
  }

  table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;

    th,
    td {
      padding: 0.75rem 1rem;
      text-align: left;
      border-bottom: 1px solid #e2e8f0;
    }

    th {
      background: #f7fafc;
      font-weight: 600;
      color: #2d3748;
      position: sticky;
      top: 0;
    }

    td {
      color: #4a5568;
    }

    tr:hover {
      background: #f7fafc;
    }
  }
}

// Trends Specific Styles
.trends-overview {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 1.5rem;
  margin-top: 1rem;

  .trend-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-radius: 8px;

    .trend-indicator {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      &.up {
        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        color: white;
      }

      &.down {
        background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
        color: white;
      }

      &.stable {
        background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        color: white;
      }
    }

    .trend-content {
      .trend-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2d3748;
      }

      .trend-label {
        font-size: 0.875rem;
        color: #718096;
        margin-bottom: 0.25rem;
      }

      .trend-status {
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        color: #4a5568;
      }
    }
  }

  .engagement-card {
    padding: 1rem;
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
    border-radius: 8px;

    h3 {
      font-size: 1rem;
      font-weight: 600;
      color: #2d3748;
      margin: 0 0 1rem 0;
    }

    .engagement-stats {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 1rem;

      .engagement-stat {
        text-align: center;

        .stat-number {
          display: block;
          font-size: 1.25rem;
          font-weight: 700;
          color: #2d3748;
        }

        .stat-label {
          font-size: 0.75rem;
          color: #718096;
          margin-top: 0.25rem;
        }
      }
    }
  }
}

.insights-section {
  padding: 1.5rem;
  border-top: 1px solid #e2e8f0;

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0 0 1rem 0;
  }

  .insights-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;

    .insight-item {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.75rem 1rem;
      background: #f7fafc;
      border-radius: 8px;
      color: #2d3748;

      svg {
        color: #48bb78;
        flex-shrink: 0;
      }
    }
  }
}

// Empty State
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  color: #718096;

  svg {
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #4a5568;
    margin: 0 0 0.5rem 0;
  }

  p {
    font-size: 1rem;
    margin: 0;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .reports-container {
    padding: 1rem;
  }

  .reports-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;

    .header-actions {
      justify-content: flex-end;
    }
  }

  .reports-controls {
    flex-direction: column;
    align-items: stretch;

    .report-type-tabs {
      flex-wrap: wrap;
    }

    .custom-date-range {
      flex-direction: column;
    }
  }

  .trends-overview {
    grid-template-columns: 1fr;
  }

  .summary-stats {
    grid-template-columns: 1fr;
  }

  .table-container {
    padding: 1rem;
  }
}