import { Component, OnInit, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatMenuModule } from '@angular/material/menu';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDividerModule } from '@angular/material/divider';

import { Router } from '@angular/router';
import { AuthService } from '@core/services/auth.service';
import { MembersService, MemberStats } from '@core/services/members.service';
import { User } from '@shared/models/user.model';
import { AddMemberDialogComponent } from './add-member-dialog/add-member-dialog.component';
import { ConfirmDeleteDialogComponent } from '@shared/components/confirm-delete-dialog/confirm-delete-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-members',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatMenuModule,
    MatDialogModule,
    MatSnackBarModule,
    MatSelectModule,
    MatCheckboxModule,
    MatTooltipModule,
    MatDividerModule
  ],
  templateUrl: './members.component.html',
  styleUrl: './members.component.scss'
})
export class MembersComponent implements OnInit {
  // Signals for reactive UI
  members = signal<User[]>([]);
  memberStats = signal<MemberStats | null>(null);
  isLoading = signal<boolean>(false);
  searchQuery = signal<string>('');
  includeInactive = signal<boolean>(false);

  // Computed values
  filteredMembers = computed(() => {
    const query = this.searchQuery().toLowerCase();
    const allMembers = this.members();

    if (!query) return allMembers;

    return allMembers.filter(member => {
      const fullName = member.fullName || `${member.firstName} ${member.lastName}`;
      return fullName.toLowerCase().includes(query) ||
        member.email.toLowerCase().includes(query) ||
        (member.phone && member.phone.toLowerCase().includes(query));
    });
  });

  // Table configuration
  displayedColumns: string[] = ['name', 'email', 'phone', 'role', 'status', 'lastLogin', 'actions'];

  constructor(
    private authService: AuthService,
    private membersService: MembersService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.loadMembers();
    this.loadMemberStats();
  }

  private loadMembers(): void {
    const currentUser = this.authService.currentUser();
    if (!currentUser?.churchId) return;

    this.isLoading.set(true);
    this.membersService.getChurchMembers(currentUser.churchId, this.includeInactive())
      .subscribe({
        next: (members) => {
          this.members.set(members);
          this.isLoading.set(false);
        },
        error: (error) => {
          console.error('Error loading members:', error);
          this.isLoading.set(false);
        }
      });
  }

  private loadMemberStats(): void {
    const currentUser = this.authService.currentUser();
    if (!currentUser?.churchId) return;

    this.membersService.getChurchMemberStats(currentUser.churchId)
      .subscribe({
        next: (stats) => {
          this.memberStats.set(stats);
        },
        error: (error) => {
          console.error('Error loading member stats:', error);
        }
      });
  }

  onSearchChange(query: string): void {
    this.searchQuery.set(query);
  }

  onIncludeInactiveChange(): void {
    this.includeInactive.set(!this.includeInactive());
    this.loadMembers();
  }

  onRefresh(): void {
    this.loadMembers();
    this.loadMemberStats();
  }

  getRoleColor(role: string): string {
    switch (role.toUpperCase()) {
      case 'ADMIN':
      case 'PASTOR':
        return 'primary';
      case 'LEADER':
        return 'accent';
      default:
        return '';
    }
  }

  getStatusColor(status: string): string {
    switch (status.toUpperCase()) {
      case 'ACTIVE':
        return 'primary';
      case 'INACTIVE':
        return 'warn';
      default:
        return '';
    }
  }

  formatDate(date: any): string {
    console.log('🗓️ formatDate called with:', date, 'Type:', typeof date);

    if (!date) {
      console.log('❌ Date is null/undefined, returning Never');
      return 'Never';
    }

    // Handle Firebase Timestamp
    if (date && typeof date.toDate === 'function') {
      const formattedDate = date.toDate().toLocaleDateString();
      console.log('✅ Firebase Timestamp formatted:', formattedDate);
      return formattedDate;
    }

    // Handle regular Date or string
    const formattedDate = new Date(date).toLocaleDateString();
    console.log('✅ Regular date formatted:', formattedDate);
    return formattedDate;
  }

  onViewMember(member: User): void {
    // TODO: Implement member detail view
    console.log('View member:', member);
  }

  onEditMember(member: User): void {
    // TODO: Implement member edit dialog
    console.log('Edit member:', member);
  }

  onDeleteMember(member: User): void {
    const fullName = member.fullName || `${member.firstName} ${member.lastName}`;

    const dialogRef = this.dialog.open(ConfirmDeleteDialogComponent, {
      width: '400px',
      data: {
        title: 'Delete Member',
        message: `Are you sure you want to delete ${fullName}? This action cannot be undone.`,
        confirmText: 'Delete',
        cancelText: 'Cancel'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.deleteMember(member);
      }
    });
  }

  private deleteMember(member: User): void {
    this.membersService.deleteMember(member.id).subscribe({
      next: () => {
        const fullName = member.fullName || `${member.firstName} ${member.lastName}`;
        this.snackBar.open(`${fullName} has been deleted successfully`, 'Close', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });
        // Refresh the members list and stats
        this.loadMembers();
        this.loadMemberStats();
      },
      error: (error) => {
        console.error('Error deleting member:', error);
        this.snackBar.open('Failed to delete member. Please try again.', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  onAddMember(): void {
    const currentUser = this.authService.currentUser();
    if (!currentUser?.churchId) {
      this.snackBar.open('No church associated with your account', 'Close', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    const dialogRef = this.dialog.open(AddMemberDialogComponent, {
      width: '600px',
      maxWidth: '95vw',
      data: { churchId: currentUser.churchId },
      disableClose: false
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Member was added successfully, refresh the list
        this.loadMembers();
        this.loadMemberStats();
      }
    });
  }

  onBulkImport(): void {
    this.router.navigate(['/members/bulk-import']);
  }
}
