import { Component, OnInit, On<PERSON><PERSON>roy, signal, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';

import { BulkImportService, ParseResult, ParsedMember, ImportProgress, ImportResult } from '../../../core/services/bulk-import.service';
import { AuthService } from '../../../core/services/auth.service';

type ImportStage = 'initial' | 'parsing' | 'preview' | 'importing' | 'complete';

@Component({
  selector: 'app-bulk-import',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
    MatTableModule,
    MatChipsModule,
    MatDialogModule,
    MatSnackBarModule
  ],
  templateUrl: './bulk-import.component.html',
  styleUrl: './bulk-import.component.scss'
})
export class BulkImportComponent implements OnInit, OnDestroy {
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

  private destroy$ = new Subject<void>();

  // Signals for reactive UI
  currentStage = signal<ImportStage>('initial');
  isDragOver = signal<boolean>(false);
  selectedFile = signal<File | null>(null);
  parseResult = signal<ParseResult | null>(null);
  importProgress = signal<ImportProgress | null>(null);
  importResult = signal<ImportResult | null>(null);
  currentUser = signal<any>(null);

  // Table columns for preview
  displayedColumns: string[] = ['rowNumber', 'firstName', 'lastName', 'email', 'phoneNumber', 'role', 'status', 'errors'];

  constructor(
    private bulkImportService: BulkImportService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.currentUser.set(this.authService.currentUser());
    
    // Subscribe to import progress
    this.bulkImportService.progress$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(progress => {
      this.importProgress.set(progress);
      if (progress?.stage === 'complete') {
        this.currentStage.set('complete');
      }
    });

    // Check if user has permission
    const user = this.currentUser();
    if (!user || !['admin', 'pastor', 'super_admin'].includes(user.role)) {
      this.snackBar.open('You do not have permission to perform bulk imports', 'Close', {
        duration: 5000
      });
      this.router.navigate(['/members']);
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.bulkImportService.clearState();
  }

  // Drag and drop handlers
  onDragOver(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver.set(true);
  }

  onDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver.set(false);
  }

  onDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();
    this.isDragOver.set(false);

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      this.handleFileSelection(files[0]);
    }
  }

  // File input handler
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.handleFileSelection(input.files[0]);
    }
  }

  // File selection logic
  private handleFileSelection(file: File): void {
    // Validate file type
    if (!file.name.toLowerCase().endsWith('.csv')) {
      this.snackBar.open('Please select a CSV file', 'Close', { duration: 3000 });
      return;
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      this.snackBar.open('File size must be less than 5MB', 'Close', { duration: 3000 });
      return;
    }

    this.selectedFile.set(file);
    this.parseCSVFile(file);
  }

  // Parse CSV file
  private parseCSVFile(file: File): void {
    this.currentStage.set('parsing');
    
    this.bulkImportService.parseCSV(file).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (result) => {
        this.parseResult.set(result);
        this.currentStage.set('preview');
        
        if (result.invalidRows > 0) {
          this.snackBar.open(
            `Parsed ${result.totalRows} rows. ${result.invalidRows} rows have errors.`,
            'Close',
            { duration: 5000 }
          );
        } else {
          this.snackBar.open(
            `Successfully parsed ${result.totalRows} rows. All data is valid!`,
            'Close',
            { duration: 3000 }
          );
        }
      },
      error: (error) => {
        console.error('Error parsing CSV:', error);
        this.snackBar.open('Failed to parse CSV file. Please check the format.', 'Close', {
          duration: 5000
        });
        this.currentStage.set('initial');
      }
    });
  }

  // Start import process
  onStartImport(): void {
    const result = this.parseResult();
    const user = this.currentUser();
    
    if (!result || !user?.churchId) {
      this.snackBar.open('Cannot start import: missing data', 'Close', { duration: 3000 });
      return;
    }

    const validMembers = result.members.filter(m => m.isValid);
    if (validMembers.length === 0) {
      this.snackBar.open('No valid members to import', 'Close', { duration: 3000 });
      return;
    }

    this.currentStage.set('importing');

    // First check for duplicates
    this.bulkImportService.checkDuplicates(validMembers, user.churchId).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (duplicates) => {
        if (duplicates.length > 0) {
          const message = `Found ${duplicates.length} duplicate email(s). Import will skip these members.`;
          this.snackBar.open(message, 'Close', { duration: 5000 });
        }

        // Proceed with import
        this.bulkImportService.importMembers(validMembers, user.churchId).pipe(
          takeUntil(this.destroy$)
        ).subscribe({
          next: (importResult) => {
            this.importResult.set(importResult);
            this.currentStage.set('complete');
            
            const message = `Import complete! ${importResult.successCount} members added, ${importResult.failureCount} failed.`;
            this.snackBar.open(message, 'Close', { duration: 5000 });
          },
          error: (error) => {
            console.error('Import failed:', error);
            this.snackBar.open('Import failed. Please try again.', 'Close', { duration: 5000 });
            this.currentStage.set('preview');
          }
        });
      },
      error: (error) => {
        console.error('Error checking duplicates:', error);
        this.snackBar.open('Error checking for duplicates. Please try again.', 'Close', {
          duration: 5000
        });
        this.currentStage.set('preview');
      }
    });
  }

  // Reset to initial state
  onReset(): void {
    this.currentStage.set('initial');
    this.selectedFile.set(null);
    this.parseResult.set(null);
    this.importProgress.set(null);
    this.importResult.set(null);
    this.bulkImportService.clearState();
    
    // Reset file input
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
  }

  // Navigate back to members
  onBackToMembers(): void {
    this.router.navigate(['/members']);
  }

  // Trigger file input click
  onSelectFile(): void {
    this.fileInput.nativeElement.click();
  }

  // Get error chip color
  getErrorChipColor(member: ParsedMember): string {
    return member.isValid ? 'primary' : 'warn';
  }

  // Get stage title
  getStageTitle(): string {
    switch (this.currentStage()) {
      case 'initial': return 'Upload CSV File';
      case 'parsing': return 'Parsing CSV File...';
      case 'preview': return 'Review Data';
      case 'importing': return 'Importing Members...';
      case 'complete': return 'Import Complete';
      default: return 'Bulk Import';
    }
  }

  // Get stage description
  getStageDescription(): string {
    switch (this.currentStage()) {
      case 'initial': 
        return 'Upload a CSV file containing member information. The file should include columns for First Name, Last Name, and Email at minimum.';
      case 'parsing': 
        return 'Processing your CSV file and validating the data...';
      case 'preview': 
        return 'Review the parsed data below. Fix any errors before importing.';
      case 'importing': 
        return 'Adding members to your church database...';
      case 'complete': 
        return 'The bulk import process has been completed.';
      default: 
        return '';
    }
  }

  // Download sample CSV template
  onDownloadTemplate(): void {
    const csvContent = 'First Name,Last Name,Email,Phone Number,Role,Status\n' +
                      'John,Doe,<EMAIL>,+233123456789,member,active\n' +
                      'Jane,Smith,<EMAIL>,+233987654321,admin,active';
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'member-import-template.csv';
    link.click();
    window.URL.revokeObjectURL(url);
  }

  // Format errors for display
  formatErrors(errors: string[]): string {
    return errors.join(', ');
  }

  // Get progress percentage
  getProgressPercentage(): number {
    const progress = this.importProgress();
    return progress ? progress.percentage : 0;
  }

  // Get progress text
  getProgressText(): string {
    const progress = this.importProgress();
    if (!progress) return '';
    
    return `${progress.processed} of ${progress.total} processed`;
  }
}
