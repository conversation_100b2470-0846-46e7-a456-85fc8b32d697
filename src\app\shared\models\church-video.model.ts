import { Timestamp } from '@angular/fire/firestore';

export interface ChurchVideo {
  id: string;
  churchId: string;
  title: string;
  description?: string;
  videoUrl: string;
  thumbnailUrl?: string;
  duration?: number;
  category: string;
  isPublic: boolean;
  uploadedBy: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface CreateChurchVideoRequest {
  churchId: string;
  title: string;
  description?: string;
  videoUrl: string;
  thumbnailUrl?: string;
  duration?: number;
  category: string;
  isPublic: boolean;
  uploadedBy: string;
}

export interface UpdateChurchVideoRequest {
  title?: string;
  description?: string;
  videoUrl?: string;
  thumbnailUrl?: string;
  duration?: number;
  category?: string;
  isPublic?: boolean;
}
