.location-picker {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.search-section {
  .full-width {
    width: 100%;
  }
}

.map-section {
  .map-container {
    height: 400px;
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 4px;
  }
}

.coordinates-section {
  h4 {
    margin: 0 0 1rem 0;
    color: #333;
  }

  .coordinates-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
}

.selection-display {
  background: #f5f5f5;
  padding: 1rem;
  border-radius: 4px;
  border-left: 4px solid #2196f3;

  h4 {
    margin: 0 0 0.5rem 0;
    color: #333;
  }

  p {
    margin: 0.25rem 0;
    font-size: 0.9rem;
  }
}
