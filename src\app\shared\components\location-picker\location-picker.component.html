<div class="location-picker">
  <!-- Address Search -->
  <div class="search-section">
    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Search Address</mat-label>
      <input matInput 
             [(ngModel)]="searchAddress" 
             (keyup.enter)="onSearchAddress()"
             placeholder="Enter church address to find coordinates">
      <button mat-icon-button matSuffix (click)="onSearchAddress()" [disabled]="isSearching()">
        @if (isSearching()) {
          <mat-spinner diameter="20"></mat-spinner>
        } @else {
          <mat-icon>search</mat-icon>
        }
      </button>
    </mat-form-field>
  </div>

  <!-- Map Container -->
  <div class="map-section">
    <div #mapContainer class="map-container"></div>
  </div>

  <!-- Manual Coordinate Entry -->
  <div class="coordinates-section">
    <h4>Manual Coordinates</h4>
    <div class="coordinates-grid">
      <mat-form-field appearance="outline">
        <mat-label>Latitude</mat-label>
        <input matInput 
               type="number" 
               [(ngModel)]="manualLatitude" 
               (ngModelChange)="onManualCoordinateChange()"
               step="0.000001"
               placeholder="e.g., 5.6037">
      </mat-form-field>
      
      <mat-form-field appearance="outline">
        <mat-label>Longitude</mat-label>
        <input matInput 
               type="number" 
               [(ngModel)]="manualLongitude" 
               (ngModelChange)="onManualCoordinateChange()"
               step="0.000001"
               placeholder="e.g., -0.1870">
      </mat-form-field>
    </div>
  </div>

  <!-- Current Selection Display -->
  @if (selectedLocation()) {
    <div class="selection-display">
      <h4>Selected Location</h4>
      <p><strong>Latitude:</strong> {{ selectedLocation()!.latitude.toFixed(6) }}</p>
      <p><strong>Longitude:</strong> {{ selectedLocation()!.longitude.toFixed(6) }}</p>
      @if (selectedLocation()!.address) {
        <p><strong>Address:</strong> {{ selectedLocation()!.address }}</p>
      }
    </div>
  }
</div>
