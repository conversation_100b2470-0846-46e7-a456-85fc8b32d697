<div class="reports-container">
  <!-- Header Section -->
  <div class="reports-header">
    <div class="header-content">
      <h1>Attendance Reports</h1>
      <p>Comprehensive attendance analytics and insights for your church</p>
    </div>

    <div class="header-actions">
      <button class="refresh-btn" (click)="onRefreshReport()" [disabled]="isLoading()">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path
            d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z" />
        </svg>
        Refresh
      </button>

      <button mat-button class="export-btn" [matMenuTriggerFor]="exportMenu">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
        </svg>
        Export
        <mat-icon>expand_more</mat-icon>
      </button>

      <mat-menu #exportMenu="matMenu">
        <button mat-menu-item (click)="onExportReport('csv')">
          <mat-icon>description</mat-icon>
          <span>Export as CSV</span>
        </button>
        <button mat-menu-item (click)="onExportReport('excel')">
          <mat-icon>table_chart</mat-icon>
          <span>Export as Excel</span>
        </button>
        <button mat-menu-item (click)="onExportReport('pdf')">
          <mat-icon>picture_as_pdf</mat-icon>
          <span>Export as PDF</span>
        </button>
      </mat-menu>
    </div>
  </div>

  <!-- Error Message -->
  @if (error()) {
  <div class="error-message">
    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
      <path
        d="M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z" />
    </svg>
    {{ error() }}
    <button (click)="onRefreshReport()" class="retry-btn">Retry</button>
  </div>
  }

  <!-- Controls Section -->
  <div class="reports-controls">
    <!-- Report Type Selection -->
    <div class="control-group">
      <label>Report Type</label>
      <div class="report-type-tabs">
        @for (option of reportTypeOptions(); track option.value) {
        <button class="tab-btn" [class.active]="selectedReportType() === option.value"
          (click)="onReportTypeChange($any(option.value))">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            @switch (option.icon) {
            @case ('calendar-week') {
            <path
              d="M19,3H18V1H16V3H8V1H6V3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V8H19V19Z" />
            }
            @case ('calendar-month') {
            <path
              d="M9,10V12H7V10H9M13,10V12H11V10H13M17,10V12H15V10H17M19,3A2,2 0 0,1 21,5V19A2,2 0 0,1 19,21H5C3.89,21 3,20.1 3,19V5A2,2 0 0,1 5,3H6V1H8V3H16V1H18V3H19M19,19V8H5V19H19M9,14V16H7V14H9M13,14V16H11V14H13M17,14V16H15V14H17Z" />
            }
            @case ('trending-up') {
            <path d="M16,6L18.29,8.29L13.41,13.17L9.41,9.17L2,16.59L3.41,18L9.41,12L13.41,16L19.71,9.71L22,12V6H16Z" />
            }
            }
          </svg>
          {{ option.label }}
        </button>
        }
      </div>
    </div>

    <!-- Date Range Selection -->
    <div class="control-group">
      <label>Date Range</label>
      <select class="date-range-select" [value]="selectedDateRange()"
        (change)="onDateRangeChange($any($event.target).value)">
        @for (option of dateRangeOptions(); track option.value) {
        <option [value]="option.value">{{ option.label }}</option>
        }
      </select>
    </div>

    <!-- Custom Date Range -->
    @if (isCustomDateRange()) {
    <div class="custom-date-range">
      <div class="date-input-group">
        <label>Start Date</label>
        <input type="date" [value]="customStartDate()"
          (change)="customStartDate.set($any($event.target).value); onCustomDateChange()" class="date-input">
      </div>
      <div class="date-input-group">
        <label>End Date</label>
        <input type="date" [value]="customEndDate()"
          (change)="customEndDate.set($any($event.target).value); onCustomDateChange()" class="date-input">
      </div>
    </div>
    }

    <!-- Advanced Filters Toggle -->
    <div class="control-group">
      <button class="advanced-filters-toggle" (click)="onToggleAdvancedFilters()">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path
            d="M14,12V19.88C14.04,20.18 13.94,20.5 13.71,20.71C13.32,21.1 12.69,21.1 12.3,20.71L10.29,18.7C10.06,18.47 9.96,18.16 10,17.87V12H9.97L4.21,4.62C3.87,4.19 3.95,3.56 4.38,3.22C4.57,3.08 4.78,3 5,3V3H19V3C19.22,3 19.43,3.08 19.62,3.22C20.05,3.56 20.13,4.19 19.79,4.62L14.03,12H14Z" />
        </svg>
        Advanced Filters
        <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor" [class.rotated]="showAdvancedFilters()">
          <path d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z" />
        </svg>
      </button>
    </div>
  </div>

  <!-- Advanced Filters Panel -->
  @if (showAdvancedFilters()) {
  <div class="advanced-filters-panel">
    <div class="filters-header">
      <h3>Advanced Filters</h3>
      <button class="reset-filters-btn" (click)="onResetFilters()">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
          <path
            d="M17.65,6.35C16.2,4.9 14.21,4 12,4C7.58,4 4.01,7.58 4.01,12C4.01,16.42 7.58,20 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18C8.69,18 6,15.31 6,12C6,8.69 8.69,6 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z" />
        </svg>
        Reset Filters
      </button>
    </div>

    <div class="filters-grid">
      <!-- Verification Status Filter -->
      <div class="filter-group">
        <label>Verification Status</label>
        <select class="filter-select" [value]="filterByVerificationStatus()"
          (change)="onVerificationFilterChange($any($event.target).value)">
          @for (option of verificationStatusOptions(); track option.value) {
          <option [value]="option.value">{{ option.label }}</option>
          }
        </select>
      </div>

      <!-- Service Type Filter -->
      <div class="filter-group">
        <label>Service Type</label>
        <select class="filter-select" [value]="filterByServiceType()"
          (change)="onServiceTypeFilterChange($any($event.target).value)">
          @for (option of serviceTypeOptions(); track option.value) {
          <option [value]="option.value">{{ option.label }}</option>
          }
        </select>
      </div>

      <!-- Minimum Attendance Filter -->
      <div class="filter-group">
        <label>Minimum Attendance</label>
        <input type="number" class="filter-input" [value]="minimumAttendance()"
          (input)="onMinimumAttendanceChange(+$any($event.target).value)" min="0" placeholder="0">
      </div>

      <!-- Active Members Only Toggle -->
      <div class="filter-group">
        <label class="checkbox-label">
          <input type="checkbox" [checked]="showOnlyActiveMembers()" (change)="onActiveOnlyToggle()">
          <span class="checkbox-custom"></span>
          Active Members Only
        </label>
      </div>
    </div>
  </div>
  }

  <!-- Loading State -->
  @if (isLoading()) {
  <div class="loading-container">
    <div class="loading-spinner"></div>
    <p>Loading report data...</p>
  </div>
  }

  <!-- Report Content -->
  @if (!isLoading() && currentReport()) {
  <div class="report-content">
    <!-- Weekly Report -->
    @if (selectedReportType() === 'weekly' && filteredWeeklyData()) {
    <div class="weekly-report">
      <div class="report-summary">
        <h2>Weekly Attendance Summary</h2>
        <div class="summary-stats">
          <div class="stat-card">
            <div class="stat-value">{{ filteredWeeklyData()!.summary.totalAttendance }}</div>
            <div class="stat-label">Total Attendance</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ filteredWeeklyData()!.summary.averageWeeklyAttendance | number:'1.0-1' }}</div>
            <div class="stat-label">Weekly Average</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ filteredWeeklyData()!.weeklyData.length }}</div>
            <div class="stat-label">Weeks Analyzed</div>
          </div>
        </div>
      </div>

      <!-- Weekly Charts -->
      @if (weeklyChartData()) {
      <div class="charts-section">
        <app-attendance-chart [chartType]="'line'" [data]="weeklyChartData()" [title]="'Weekly Attendance Trends'"
          [loading]="isLoading()">
        </app-attendance-chart>

        @if (verificationChartData()) {
        <app-attendance-chart [chartType]="'bar'" [data]="verificationChartData()" [title]="'Weekly Verification Rates'"
          [loading]="isLoading()">
        </app-attendance-chart>
        }
      </div>
      }

      <div class="weekly-data-table">
        <h3>Weekly Breakdown</h3>
        <div class="table-container">
          <table>
            <thead>
              <tr>
                <th>Week Starting</th>
                <th>Total Attendance</th>
                <th>Unique Members</th>
                <th>Services Held</th>
                <th>Verification Rate</th>
                <th>Avg per Service</th>
              </tr>
            </thead>
            <tbody>
              @for (week of weeklyReport()!.weeklyData; track week.weekStart) {
              <tr>
                <td>{{ week.weekStart | date:'mediumDate' }}</td>
                <td>{{ week.totalAttendance }}</td>
                <td>{{ week.uniqueMembers }}</td>
                <td>{{ week.servicesHeld }}</td>
                <td>{{ week.verificationRate | number:'1.0-1' }}%</td>
                <td>{{ week.averagePerService | number:'1.0-1' }}</td>
              </tr>
              }
            </tbody>
          </table>
        </div>
      </div>
    </div>
    }

    <!-- Monthly Report -->
    @if (selectedReportType() === 'monthly' && monthlyReport()) {
    <div class="monthly-report">
      <div class="report-summary">
        <h2>Monthly Attendance Summary</h2>
        <div class="summary-stats">
          <div class="stat-card">
            <div class="stat-value">{{ monthlyReport()!.summary.totalAttendance }}</div>
            <div class="stat-label">Total Attendance</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ monthlyReport()!.summary.averageMonthlyAttendance | number:'1.0-1' }}</div>
            <div class="stat-label">Monthly Average</div>
          </div>
          <div class="stat-card">
            <div class="stat-value">{{ monthlyReport()!.totalMonths }}</div>
            <div class="stat-label">Months Analyzed</div>
          </div>
        </div>
      </div>

      <!-- Monthly Charts -->
      @if (monthlyChartData()) {
      <div class="charts-section">
        <app-attendance-chart [chartType]="'line'" [data]="monthlyChartData()" [title]="'Monthly Attendance Trends'"
          [loading]="isLoading()">
        </app-attendance-chart>

        @if (verificationChartData()) {
        <app-attendance-chart [chartType]="'bar'" [data]="verificationChartData()"
          [title]="'Monthly Verification Rates'" [loading]="isLoading()">
        </app-attendance-chart>
        }
      </div>
      }

      <div class="monthly-data-table">
        <h3>Monthly Breakdown</h3>
        <div class="table-container">
          <table>
            <thead>
              <tr>
                <th>Month</th>
                <th>Total Attendance</th>
                <th>Unique Members</th>
                <th>Services Held</th>
                <th>Weekend Services</th>
                <th>Verification Rate</th>
                <th>Avg per Service</th>
              </tr>
            </thead>
            <tbody>
              @for (month of monthlyReport()!.monthlyData; track month.month) {
              <tr>
                <td>{{ month.month }}</td>
                <td>{{ month.totalAttendance }}</td>
                <td>{{ month.uniqueMembers }}</td>
                <td>{{ month.servicesHeld }}</td>
                <td>{{ month.weekendServices }}</td>
                <td>{{ month.verificationRate | number:'1.0-1' }}%</td>
                <td>{{ month.averagePerService | number:'1.0-1' }}</td>
              </tr>
              }
            </tbody>
          </table>
        </div>
      </div>
    </div>
    }

    <!-- Trends Report -->
    @if (selectedReportType() === 'trends' && trendsReport()) {
    <div class="trends-report">
      <div class="report-summary">
        <h2>Attendance Trends & Analytics</h2>
        <div class="trends-overview">
          <div class="trend-card">
            <div class="trend-indicator" [class]="trendsReport()!.trends.growth.direction">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                @if (trendsReport()!.trends.growth.direction === 'up') {
                <path
                  d="M16,6L18.29,8.29L13.41,13.17L9.41,9.17L2,16.59L3.41,18L9.41,12L13.41,16L19.71,9.71L22,12V6H16Z" />
                } @else if (trendsReport()!.trends.growth.direction === 'down') {
                <path
                  d="M16,18L18.29,15.71L13.41,10.83L9.41,14.83L2,7.41L3.41,6L9.41,12L13.41,8L19.71,14.29L22,12V18H16Z" />
                } @else {
                <path d="M22,12L18,8V11H3V13H18V16L22,12Z" />
                }
              </svg>
            </div>
            <div class="trend-content">
              <div class="trend-value">{{ trendsReport()!.trends.growth.growthRate | number:'1.0-1' }}%</div>
              <div class="trend-label">Growth Rate</div>
              <div class="trend-status">{{ trendsReport()!.trends.growth.trend | titlecase }}</div>
            </div>
          </div>

          <div class="engagement-card">
            <h3>Member Engagement</h3>
            <div class="engagement-stats">
              <div class="engagement-stat">
                <span class="stat-number">{{ trendsReport()!.trends.engagement.regularAttenders }}</span>
                <span class="stat-label">Regular Attenders</span>
              </div>
              <div class="engagement-stat">
                <span class="stat-number">{{ trendsReport()!.trends.engagement.occasionalAttenders }}</span>
                <span class="stat-label">Occasional Attenders</span>
              </div>
              <div class="engagement-stat">
                <span class="stat-number">{{ trendsReport()!.trends.engagement.infrequentAttenders }}</span>
                <span class="stat-label">Infrequent Attenders</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      @if (trendsReport()!.insights.length > 0) {
      <div class="insights-section">
        <h3>Key Insights</h3>
        <div class="insights-list">
          @for (insight of trendsReport()!.insights; track $index) {
          <div class="insight-item">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path
                d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z" />
            </svg>
            {{ insight }}
          </div>
          }
        </div>
      </div>
      }
    </div>
    }
  </div>
  }

  <!-- Empty State -->
  @if (!isLoading() && !currentReport() && !error()) {
  <div class="empty-state">
    <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
      <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
    </svg>
    <h3>No Report Data Available</h3>
    <p>Select a report type and date range to view attendance analytics.</p>
  </div>
  }
</div>