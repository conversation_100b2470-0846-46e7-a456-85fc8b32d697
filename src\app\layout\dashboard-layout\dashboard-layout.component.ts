import { Component, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { AuthService } from '../../core/services/auth.service';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { HeaderComponent } from '../header/header.component';

@Component({
  selector: 'app-dashboard-layout',
  standalone: true,
  imports: [CommonModule, RouterOutlet, SidebarComponent, HeaderComponent],
  templateUrl: './dashboard-layout.component.html',
  styleUrl: './dashboard-layout.component.scss'
})
export class DashboardLayoutComponent {
  isSidebarCollapsed = signal(false);
  isMobileMenuOpen = signal(false);

  constructor(private authService: AuthService) {}

  onToggleSidebar(): void {
    this.isSidebarCollapsed.update(collapsed => !collapsed);
  }

  onToggleMobileMenu(): void {
    this.isMobileMenuOpen.update(open => !open);
  }

  onCloseMobileMenu(): void {
    this.isMobileMenuOpen.set(false);
  }

  get currentUser() {
    return this.authService.currentUser;
  }

  get isAuthenticated() {
    return this.authService.isAuthenticated;
  }
}
