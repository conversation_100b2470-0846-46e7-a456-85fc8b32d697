.church-registration-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.registration-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 600px;
}

.header {
  text-align: center;
  margin-bottom: 30px;

  h1 {
    color: #333;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 8px;
  }

  p {
    color: #666;
    font-size: 1rem;
  }
}

.progress-indicator {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 2px;
    background: #e0e0e0;
    z-index: 1;
  }

  .step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;

    &:not(:last-child) {
      margin-right: 100px;
    }

    .step-number {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #e0e0e0;
      color: #999;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      margin-bottom: 8px;
      transition: all 0.3s ease;
    }

    .step-label {
      font-size: 0.875rem;
      color: #666;
      font-weight: 500;
    }

    &.active .step-number {
      background: #667eea;
      color: white;
    }

    &.completed .step-number {
      background: #4caf50;
      color: white;
    }
  }
}

.error-message {
  background: #ffebee;
  color: #c62828;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 20px;
  border-left: 4px solid #c62828;
}

.registration-form {
  .form-group {
    margin-bottom: 20px;

    label {
      display: block;
      margin-bottom: 6px;
      font-weight: 500;
      color: #333;
    }

    input, textarea {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e0e0e0;
      border-radius: 6px;
      font-size: 1rem;
      transition: border-color 0.3s ease;

      &:focus {
        outline: none;
        border-color: #667eea;
      }

      &.error {
        border-color: #c62828;
      }

      &::placeholder {
        color: #999;
      }
    }

    textarea {
      resize: vertical;
      min-height: 80px;
    }

    .checkbox-label {
      display: flex;
      align-items: center;
      cursor: pointer;
      font-weight: normal;

      input[type="checkbox"] {
        width: auto;
        margin-right: 8px;
      }
    }
  }

  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .field-error {
    color: #c62828;
    font-size: 0.875rem;
    margin-top: 4px;
    display: block;
  }
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 30px;

  .btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    &.btn-primary {
      background: #667eea;
      color: white;

      &:hover:not(:disabled) {
        background: #5a6fd8;
      }
    }

    &.btn-secondary {
      background: #f5f5f5;
      color: #333;

      &:hover:not(:disabled) {
        background: #e0e0e0;
      }
    }
  }
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.footer {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;

  p {
    color: #666;
    margin: 0;

    a {
      color: #667eea;
      text-decoration: none;
      font-weight: 500;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

@media (max-width: 768px) {
  .church-registration-container {
    padding: 10px;
  }

  .registration-card {
    padding: 30px 20px;
  }

  .progress-indicator {
    .step:not(:last-child) {
      margin-right: 60px;
    }

    &::before {
      width: 60px;
    }
  }

  .form-actions {
    flex-direction: column;

    .btn {
      width: 100%;
      justify-content: center;
    }
  }
}
