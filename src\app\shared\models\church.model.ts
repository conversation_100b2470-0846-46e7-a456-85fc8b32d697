import { Timestamp } from '@angular/fire/firestore';

export interface Church {
  id: string;
  name: string;
  branch?: string;
  address: string;
  latitude: number;
  longitude: number;
  description?: string;
  imageUrl?: string;
  phone?: string;
  email?: string;
  adminName?: string;
  adminPhone?: string;
  adminEmail?: string;
  serviceTypes: string[];
  serviceTimes: ServiceTime[];
  adminIds: string[];
  settings: ChurchSettings;
  geofenceRadius?: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface ChurchSettings {
  allowSelfRegistration: boolean;
  requireApproval: boolean;
  geofenceRadius: number;
}

export interface ServiceTime {
  day?: string;
  date?: string;
  startTime: string;
  endTime: string;
  name: string;
}

export interface CreateChurchRequest {
  name: string;
  branch?: string;
  address: string;
  latitude: number;
  longitude: number;
  description?: string;
  imageUrl?: string;
  phone?: string;
  email?: string;
  adminName?: string;
  adminPhone?: string;
  adminEmail?: string;
  serviceTypes: string[];
  serviceTimes: ServiceTime[];
  adminIds: string[];
  settings: ChurchSettings;
  geofenceRadius?: number;
}

export interface UpdateChurchRequest {
  name?: string;
  branch?: string;
  address?: string;
  latitude?: number;
  longitude?: number;
  description?: string;
  imageUrl?: string;
  phone?: string;
  email?: string;
  adminName?: string;
  adminPhone?: string;
  adminEmail?: string;
  serviceTypes?: string[];
  serviceTimes?: ServiceTime[];
  adminIds?: string[];
  settings?: ChurchSettings;
  geofenceRadius?: number;
}
