<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Superadmin User</title>
    <script type="module">
        // Import the functions you need from the SDKs you need
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, createUserWithEmailAndPassword } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFirestore, doc, setDoc, Timestamp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Your web app's Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBeTHmwWp55xfYZzYjlyb2aVlgBya-e-2Y",
            authDomain: "flockin-church-app.firebaseapp.com",
            projectId: "flockin-church-app",
            storageBucket: "flockin-church-app.firebasestorage.app",
            messagingSenderId: "917774659789",
            appId: "1:917774659789:web:bcad4bd51bad6c9df97984"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        window.createSuperAdmin = async function () {
            const email = '<EMAIL>';
            const password = 'SuperAdmin123!';

            try {
                console.log('Creating superadmin user...');
                document.getElementById('status').innerHTML = 'Creating superadmin user...';

                // Create user with Firebase Auth
                const userCredential = await createUserWithEmailAndPassword(auth, email, password);
                const user = userCredential.user;

                console.log('Firebase Auth user created:', user.uid);
                document.getElementById('status').innerHTML = 'Firebase Auth user created: ' + user.uid;

                // Create user document in Firestore
                const userData = {
                    id: user.uid,
                    email: email,
                    firstName: 'Super',
                    lastName: 'Admin',
                    churchId: null, // Super admin doesn't belong to a specific church
                    role: 'super_admin',
                    status: 'active',
                    createdAt: Timestamp.now(),
                    updatedAt: Timestamp.now()
                };

                await setDoc(doc(db, 'users', user.uid), userData);

                console.log('Superadmin user document created in Firestore');
                document.getElementById('status').innerHTML = `
                    <div style="color: green;">
                        <h3>✅ Superadmin user created successfully!</h3>
                        <p><strong>Email:</strong> ${email}</p>
                        <p><strong>Password:</strong> ${password}</p>
                        <p><strong>Role:</strong> super_admin</p>
                        <p><strong>User ID:</strong> ${user.uid}</p>
                        <p style="color: orange;"><strong>⚠️ Please change the password after first login!</strong></p>
                    </div>
                `;

            } catch (error) {
                console.error('Error creating superadmin:', error);
                document.getElementById('status').innerHTML = `
                    <div style="color: red;">
                        <h3>❌ Error creating superadmin:</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        };
    </script>
</head>

<body>
    <div style="max-width: 600px; margin: 50px auto; padding: 20px; font-family: Arial, sans-serif;">
        <h1>Create Superadmin User</h1>
        <p>This will create a superadmin user for the Flockin Church Admin Dashboard.</p>

        <button onclick="createSuperAdmin()"
            style="background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px;">
            Create Superadmin User
        </button>

        <div id="status"
            style="margin-top: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 4px; background: #f9f9f9;">
            Click the button above to create the superadmin user.
        </div>
    </div>
</body>

</html>