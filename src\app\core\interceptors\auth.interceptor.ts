import { HttpInterceptorFn, HttpRequest, HttpHandlerFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { catchError, throwError } from 'rxjs';
import { AuthService } from '@core/services/auth.service';

export const authInterceptor: HttpInterceptorFn = (req: HttpRequest<unknown>, next: HttpHandlerFn) => {
  const authService = inject(AuthService);

  // For Firebase, we don't need to add tokens to HTTP requests
  // Firebase SDK handles authentication automatically
  return next(req).pipe(
    catchError(error => {
      // Handle 401 errors by redirecting to login
      if (error.status === 401) {
        authService.signOut();
      }
      return throwError(() => error);
    })
  );
};
