// Firebase Messaging Service Worker
// This file handles background notifications when the app is not in focus

importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

// Initialize Firebase
firebase.initializeApp({
  apiKey: "AIzaSyBeTHmwWp55xfYZzYjlyb2aVlgBya-e-2Y",
  authDomain: "flockin-church-app.firebaseapp.com",
  projectId: "flockin-church-app",
  storageBucket: "flockin-church-app.firebasestorage.app",
  messagingSenderId: "917774659789",
  appId: "1:917774659789:web:bcad4bd51bad6c9df97984"
});

// Retrieve Firebase Messaging object
const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage((payload) => {
  console.log('Received background message:', payload);

  const notificationTitle = payload.notification?.title || 'Church Notification';
  const notificationOptions = {
    body: payload.notification?.body || 'You have a new message',
    icon: payload.notification?.icon || '/favicon.ico',
    image: payload.notification?.image,
    badge: '/assets/icons/badge-icon.png',
    tag: 'church-notification',
    data: payload.data || {},
    actions: [
      {
        action: 'open',
        title: 'Open App',
        icon: '/assets/icons/open-icon.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
        icon: '/assets/icons/dismiss-icon.png'
      }
    ],
    requireInteraction: true,
    silent: false
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification click events
self.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event);
  
  event.notification.close();

  if (event.action === 'dismiss') {
    return;
  }

  // Handle notification click - open the app
  const urlToOpen = event.notification.data?.url || '/dashboard';
  
  event.waitUntil(
    clients.matchAll({
      type: 'window',
      includeUncontrolled: true
    }).then((clientList) => {
      // Check if there's already a window/tab open with the target URL
      for (const client of clientList) {
        if (client.url === urlToOpen && 'focus' in client) {
          return client.focus();
        }
      }
      
      // If no window/tab is open, open a new one
      if (clients.openWindow) {
        return clients.openWindow(urlToOpen);
      }
    })
  );
});

// Handle notification close events
self.addEventListener('notificationclose', (event) => {
  console.log('Notification closed:', event);
  
  // Track notification dismissal if needed
  if (event.notification.data?.trackDismissal) {
    // Send analytics or tracking data
    fetch('/api/notifications/dismissed', {
      method: 'POST',
      body: JSON.stringify({
        notificationId: event.notification.data.notificationId,
        dismissedAt: new Date().toISOString()
      }),
      headers: {
        'Content-Type': 'application/json'
      }
    }).catch(error => {
      console.error('Failed to track notification dismissal:', error);
    });
  }
});

// Handle push events (for custom push handling if needed)
self.addEventListener('push', (event) => {
  console.log('Push event received:', event);
  
  if (event.data) {
    const data = event.data.json();
    console.log('Push data:', data);
    
    // Custom push handling logic can go here
    // This is typically handled by Firebase Messaging automatically
  }
});

// Service worker installation
self.addEventListener('install', (event) => {
  console.log('Firebase Messaging Service Worker installed');
  self.skipWaiting();
});

// Service worker activation
self.addEventListener('activate', (event) => {
  console.log('Firebase Messaging Service Worker activated');
  event.waitUntil(self.clients.claim());
});
