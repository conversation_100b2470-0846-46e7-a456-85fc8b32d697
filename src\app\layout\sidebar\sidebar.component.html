<aside class="sidebar" [class.collapsed]="isCollapsed" [class.mobile-open]="isMobileOpen">

  <!-- Logo Section -->
  <div class="sidebar-header">
    <div class="logo">
      @if (!isCollapsed) {
      <h1>Flockin</h1>
      <span class="tagline">Dashboard</span>
      } @else {
      <div class="logo-icon">F</div>
      }
    </div>
  </div>

  <!-- Navigation Menu -->
  <mat-nav-list class="sidebar-nav">
    @for (item of filteredMenuItems; track item.route) {
    <mat-list-item [routerLink]="item.route" routerLinkActive="active">
      <mat-icon matListItemIcon>{{ getMatIcon(item.icon) }}</mat-icon>
      @if (!isCollapsed) {
      <span matListItemTitle>{{ item.label }}</span>
      }
    </mat-list-item>
    }
  </mat-nav-list>

  <!-- User Section -->
  <div class="sidebar-footer">
    <mat-list-item button (click)="onLogout()">
      <mat-icon matListItemIcon>logout</mat-icon>
      @if (!isCollapsed) {
      <span matListItemTitle>Sign Out</span>
      }
    </mat-list-item>
  </div>
</aside>