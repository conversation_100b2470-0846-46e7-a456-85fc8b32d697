import { Timestamp } from '@angular/fire/firestore';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  churchId: string | null;
  role: 'super_admin' | 'admin' | 'pastor' | 'member';
  profilePicture?: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  address?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  status?: 'active' | 'inactive' | 'pending';
  lastLoginAt?: Timestamp;
  fullName?: string; // Computed property
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface CreateUserRequest {
  email: string;
  firstName: string;
  lastName: string;
  password?: string; // Optional - used when creating from admin dashboard
  churchId?: string;
  role: 'super_admin' | 'admin' | 'pastor' | 'member';
  profilePicture?: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  address?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  status?: 'active' | 'inactive' | 'pending';
}

export interface UpdateUserRequest {
  firstName?: string;
  lastName?: string;
  churchId?: string;
  role?: 'super_admin' | 'admin' | 'pastor' | 'member';
  profilePicture?: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  address?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  status?: 'active' | 'inactive' | 'pending';
  lastLoginAt?: Timestamp;
}
