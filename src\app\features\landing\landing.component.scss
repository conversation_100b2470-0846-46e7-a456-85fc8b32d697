// Variables
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --hero-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  --text-primary: #1a202c;
  --text-secondary: #64748b;
  --background-light: linear-gradient(135deg, #f8fafc 0%, #e3f2fd 50%, #f3e5f5 100%);
  --background-section: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  --white: #ffffff;
  --border-color: #e2e8f0;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --shadow-colored: 0 10px 25px -5px rgba(102, 126, 234, 0.25);
}

// Animations
@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Host component
:host {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

// Global styles
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  min-height: 100vh;
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

// Header
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);

  .toolbar {
    background: transparent;
    box-shadow: none;
    padding: 0;

    .toolbar-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1.5rem;
    }
  }

  .logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
    font-size: 1.25rem;
    color: var(--text-primary);

    .logo-icon {
      color: #667eea;
      font-size: 1.75rem;
    }
  }

  .nav-links {
    display: flex;
    align-items: center;
    gap: 1rem;

    .login-btn {
      color: var(--text-secondary);
    }

    .signup-btn {
      background: var(--primary-gradient);
      color: white;
      border: none;
      font-weight: 600;

      &:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
      }
    }
  }

  .mobile-menu {
    display: none;
  }
}

// Hero Section
.hero {
  padding: 8rem 0 4rem;
  background: var(--hero-gradient);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
  }

  .hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
  }

  .hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    color: white;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .gradient-text {
      background: var(--accent-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-shadow: none;
    }
  }

  .hero-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin-bottom: 2rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .hero-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;

    .cta-primary {
      background: var(--success-gradient);
      color: white;
      border: none;
      padding: 0.875rem 2rem;
      font-size: 1.1rem;
      font-weight: 600;
      border-radius: 12px;
      box-shadow: var(--shadow-colored);
      animation: pulse 3s ease-in-out infinite;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 30px -5px rgba(67, 233, 123, 0.4);
        background: var(--warning-gradient);
        animation: none;
      }

      mat-icon {
        margin-right: 0.5rem;
      }
    }

    .cta-secondary {
      border: 2px solid rgba(255, 255, 255, 0.3);
      color: white;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      padding: 0.875rem 2rem;
      font-size: 1.1rem;
      font-weight: 600;
      border-radius: 12px;

      &:hover {
        border-color: rgba(255, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-1px);
      }

      mat-icon {
        margin-right: 0.5rem;
      }
    }
  }

  .app-downloads {
    .download-text {
      font-size: 0.875rem;
      color: var(--text-secondary);
      margin-bottom: 1rem;
    }

    .download-buttons {
      display: flex;
      gap: 1rem;
    }

    .app-store-btn {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.75rem 1.5rem;
      background: #000;
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &:hover:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }

      &.android-btn {
        background: linear-gradient(135deg, #01875f 0%, #00a86b 100%);

        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #00a86b 0%, #01875f 100%);
          box-shadow: 0 4px 15px rgba(1, 135, 95, 0.3);
        }

        &:disabled {
          background: #6b7280;
        }
      }

      .store-icon {
        width: 24px;
        height: 24px;
        flex-shrink: 0;
      }

      mat-spinner {
        flex-shrink: 0;
      }

      .store-text {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        flex: 1;

        .download-on {
          font-size: 0.75rem;
          opacity: 0.9;
          transition: all 0.2s ease;
        }

        .store-name {
          font-size: 1rem;
          font-weight: 600;
          display: flex;
          align-items: center;
          gap: 0.5rem;

          .file-size {
            font-size: 0.7rem;
            opacity: 0.7;
            font-weight: 400;
          }
        }
      }

      &.direct-download {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
          transition: left 0.5s ease;
        }

        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);

          &::before {
            left: 100%;
          }
        }

        &:disabled {
          background: #6b7280;

          &::before {
            display: none;
          }
        }
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background: #666 !important;

        &:hover {
          transform: none;
          box-shadow: none;
        }
      }
    }

    .download-info {
      margin-top: 1rem;
      padding: 1rem;
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 8px;
      display: flex;
      align-items: flex-start;
      gap: 0.75rem;

      .info-icon {
        color: #856404;
        margin-top: 0.125rem;
        flex-shrink: 0;
      }

      .info-text {
        font-size: 0.875rem;
        color: #856404;
        margin: 0;
        line-height: 1.4;

        .help-link {
          color: var(--primary);
          text-decoration: none;
          font-weight: 500;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
}

// Phone Mockup
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;

  .phone-mockup {
    width: 280px;
    height: 560px;
    background: #1a1a1a;
    border-radius: 30px;
    padding: 20px;
    box-shadow: var(--shadow-xl);
    position: relative;
    animation: float 6s ease-in-out infinite;

    &::before {
      content: '';
      position: absolute;
      top: 10px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 4px;
      background: #333;
      border-radius: 2px;
    }

    .phone-screen {
      width: 100%;
      height: 100%;
      background: white;
      border-radius: 20px;
      overflow: hidden;
    }

    .app-interface {
      padding: 1.5rem;
      height: 100%;

      .app-header {
        text-align: center;
        margin-bottom: 2rem;

        .status-bar {
          height: 20px;
          background: var(--background-light);
          border-radius: 10px;
          margin-bottom: 1rem;
        }

        h3 {
          color: var(--text-primary);
          font-size: 1.25rem;
          font-weight: 600;
        }
      }

      .check-in-card {
        background: var(--primary-gradient);
        color: white;
        padding: 1.5rem;
        border-radius: 16px;
        text-align: center;
        margin-bottom: 2rem;

        .check-icon {
          font-size: 3rem;
          margin-bottom: 0.5rem;
        }

        p {
          font-size: 1.1rem;
          font-weight: 600;
          margin-bottom: 0.5rem;
        }

        .time {
          opacity: 0.9;
          font-size: 0.9rem;
        }
      }

      .stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;

        .stat {
          text-align: center;
          padding: 1rem;
          background: var(--background-light);
          border-radius: 12px;

          .number {
            display: block;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
          }

          .label {
            font-size: 0.875rem;
            color: var(--text-secondary);
          }
        }
      }
    }
  }
}

// Features Section
.features {
  padding: 5rem 0;
  background: var(--background-section);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(102,126,234,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.5;
    pointer-events: none;
  }

  .section-header {
    text-align: center;
    margin-bottom: 4rem;

    h2 {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--text-primary);
      margin-bottom: 1rem;
    }

    p {
      font-size: 1.25rem;
      color: var(--text-secondary);
    }
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;

    .feature-card {
      border: 1px solid var(--border-color);
      border-radius: 16px;
      transition: all 0.3s ease;
      cursor: pointer;
      background: white;
      box-shadow: var(--shadow-sm);

      &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-colored);
        border-color: transparent;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
      }

      mat-card-content {
        padding: 2rem;
        text-align: center;

        .feature-icon {
          width: 80px;
          height: 80px;
          border-radius: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 1.5rem;
          transition: all 0.3s ease;

          mat-icon {
            font-size: 2rem;
            color: white;
          }
        }

        // Different colors for each feature
        &:nth-child(1) .feature-icon {
          background: var(--primary-gradient);
        }

        &:nth-child(2) .feature-icon {
          background: var(--success-gradient);
        }

        &:nth-child(3) .feature-icon {
          background: var(--warning-gradient);
        }

        &:nth-child(4) .feature-icon {
          background: var(--accent-gradient);
        }

        &:nth-child(5) .feature-icon {
          background: var(--secondary-gradient);
        }

        &:nth-child(6) .feature-icon {
          background: var(--primary-gradient);
        }

        &:hover .feature-icon {
          transform: scale(1.1) rotate(5deg);
        }

        h3 {
          font-size: 1.5rem;
          font-weight: 600;
          color: var(--text-primary);
          margin-bottom: 1rem;
        }

        p {
          color: var(--text-secondary);
          line-height: 1.6;
        }
      }
    }
  }
}

// Benefits Section
.benefits {
  padding: 5rem 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e3f2fd 50%, #f3e5f5 100%);

  .benefits-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;

    .benefits-text {
      h2 {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 2rem;
      }

      .benefit-list {
        display: flex;
        flex-direction: column;
        gap: 2rem;

        .benefit-item {
          display: flex;
          align-items: flex-start;
          gap: 1rem;

          .benefit-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
            transition: all 0.3s ease;
          }

          &:nth-child(1) .benefit-icon {
            background: var(--success-gradient);
          }

          &:nth-child(2) .benefit-icon {
            background: var(--warning-gradient);
          }

          &:nth-child(3) .benefit-icon {
            background: var(--accent-gradient);
          }

          &:hover .benefit-icon {
            transform: scale(1.1) rotate(5deg);
          }

          h4 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
          }

          p {
            color: var(--text-secondary);
            line-height: 1.6;
          }
        }
      }
    }

    .benefits-visual {
      .dashboard-preview {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        box-shadow: var(--shadow-lg);

        .dashboard-header {
          text-align: center;
          margin-bottom: 2rem;

          h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
          }
        }

        .dashboard-stats {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 1.5rem;

          .stat-card {
            text-align: center;
            padding: 1.5rem;
            border-radius: 12px;
            transition: all 0.3s ease;
            cursor: pointer;

            &:nth-child(1) {
              background: linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%);
              border: 1px solid rgba(67, 233, 123, 0.2);
            }

            &:nth-child(2) {
              background: linear-gradient(135deg, rgba(250, 112, 154, 0.1) 0%, rgba(254, 225, 64, 0.1) 100%);
              border: 1px solid rgba(250, 112, 154, 0.2);
            }

            &:nth-child(3) {
              background: linear-gradient(135deg, rgba(79, 172, 254, 0.1) 0%, rgba(0, 242, 254, 0.1) 100%);
              border: 1px solid rgba(79, 172, 254, 0.2);
            }

            &:nth-child(4) {
              background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
              border: 1px solid rgba(102, 126, 234, 0.2);
            }

            &:hover {
              transform: translateY(-2px);
              box-shadow: var(--shadow-lg);
            }

            .stat-number {
              display: block;
              font-size: 2rem;
              font-weight: 700;
              background: var(--primary-gradient);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
              margin-bottom: 0.5rem;
            }

            .stat-label {
              color: var(--text-secondary);
              font-size: 0.875rem;
            }
          }
        }
      }
    }
  }
}

// Testimonials Section
.testimonials {
  padding: 5rem 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 50%, #fff5f8 100%);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="stars" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="2" fill="rgba(102,126,234,0.1)"/><circle cx="10" cy="10" r="1" fill="rgba(250,112,154,0.1)"/><circle cx="40" cy="15" r="1.5" fill="rgba(67,233,123,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23stars)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
  }

  .section-header {
    text-align: center;
    margin-bottom: 4rem;

    h2 {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--text-primary);
      margin-bottom: 1rem;
    }

    p {
      font-size: 1.25rem;
      color: var(--text-secondary);
    }
  }

  .testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;

    .testimonial-card {
      border: 1px solid var(--border-color);
      border-radius: 16px;
      transition: all 0.3s ease;
      background: white;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient);
      }

      &:nth-child(1)::before {
        background: var(--success-gradient);
      }

      &:nth-child(2)::before {
        background: var(--warning-gradient);
      }

      &:nth-child(3)::before {
        background: var(--accent-gradient);
      }

      &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-colored);
        border-color: transparent;
      }

      mat-card-content {
        padding: 2rem;

        .quote-icon {
          text-align: center;
          margin-bottom: 1rem;

          mat-icon {
            font-size: 2rem;
            color: #667eea;
          }
        }

        .quote {
          font-size: 1.1rem;
          line-height: 1.6;
          color: var(--text-primary);
          margin-bottom: 2rem;
          font-style: italic;
        }

        .testimonial-author {
          display: flex;
          align-items: center;
          gap: 1rem;

          .author-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            mat-icon {
              color: white;
            }
          }



          .author-info {
            h4 {
              font-size: 1rem;
              font-weight: 600;
              color: var(--text-primary);
              margin-bottom: 0.25rem;
            }

            p {
              font-size: 0.875rem;
              color: var(--text-secondary);
            }
          }
        }
      }

      // Different colored avatars for each testimonial
      &:nth-child(1) .author-avatar {
        background: var(--success-gradient);
      }

      &:nth-child(2) .author-avatar {
        background: var(--warning-gradient);
      }

      &:nth-child(3) .author-avatar {
        background: var(--accent-gradient);
      }
    }
  }
}

// CTA Section
.cta-section {
  padding: 5rem 0;
  background: var(--secondary-gradient);
  color: white;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.5;
    pointer-events: none;
  }

  .cta-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;

    h2 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 1rem;
    }

    p {
      font-size: 1.25rem;
      margin-bottom: 2.5rem;
      opacity: 0.9;
    }

    .cta-actions {
      display: flex;
      justify-content: center;
      gap: 1rem;
      margin-bottom: 1.5rem;

      .cta-primary {
        background: var(--success-gradient);
        color: white;
        border: none;
        padding: 0.875rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 12px;
        box-shadow: 0 10px 25px -5px rgba(67, 233, 123, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 15px 30px -5px rgba(67, 233, 123, 0.5);
          background: var(--accent-gradient);
        }

        mat-icon {
          margin-right: 0.5rem;
        }
      }

      .cta-secondary {
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        padding: 0.875rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 12px;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(255, 255, 255, 0.5);
          transform: translateY(-1px);
        }

        mat-icon {
          margin-right: 0.5rem;
        }
      }
    }

    .cta-note {
      font-size: 0.875rem;
      opacity: 0.8;
    }
  }
}

// Footer
.footer {
  padding: 3rem 0 1rem;
  background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  color: white;
  position: relative;
  z-index: 1;
  margin-top: auto;

  .footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    margin-bottom: 2rem;

    .footer-brand {
      .logo {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;

        .logo-icon {
          color: #667eea;
          font-size: 1.75rem;
        }

        .logo-text {
          font-weight: 700;
          font-size: 1.25rem;
        }
      }

      p {
        color: rgba(255, 255, 255, 0.7);
        line-height: 1.6;
      }
    }

    .footer-links {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 2rem;

      .link-group {
        h4 {
          font-size: 1rem;
          font-weight: 600;
          margin-bottom: 1rem;
        }

        a {
          display: block;
          color: rgba(255, 255, 255, 0.7);
          text-decoration: none;
          margin-bottom: 0.5rem;
          transition: color 0.2s ease;

          &:hover {
            color: white;
          }
        }
      }
    }
  }

  .footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);

    p {
      color: rgba(255, 255, 255, 0.7);
      font-size: 0.875rem;
    }

    .social-links {
      display: flex;
      gap: 0.5rem;

      button {
        color: rgba(255, 255, 255, 0.7);
        transition: color 0.2s ease;

        &:hover {
          color: white;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .hero {
    .hero-content {
      gap: 2rem;
    }

    .hero-title {
      font-size: 3rem;
    }
  }

  .benefits {
    .benefits-content {
      gap: 2rem;
    }
  }
}

@media (max-width: 768px) {
  .header {
    .nav-links {
      display: none;
    }

    .mobile-menu {
      display: block;
    }
  }

  .hero {
    padding: 6rem 0 3rem;

    .hero-content {
      grid-template-columns: 1fr;
      gap: 3rem;
      text-align: center;
    }

    .hero-title {
      font-size: 2.5rem;
    }

    .hero-actions {
      flex-direction: column;
      align-items: center;

      .cta-primary,
      .cta-secondary {
        width: 100%;
        max-width: 300px;
      }
    }

    .app-downloads {
      .download-buttons {
        flex-direction: column;
        align-items: center;

        .app-store-btn {
          width: 100%;
          max-width: 200px;
        }
      }
    }
  }

  .features {
    .features-grid {
      grid-template-columns: 1fr;
    }

    .section-header {
      h2 {
        font-size: 2rem;
      }
    }
  }

  .benefits {
    .benefits-content {
      grid-template-columns: 1fr;
      gap: 3rem;
    }

    .benefits-text {
      h2 {
        font-size: 2rem;
      }
    }
  }

  .testimonials {
    .testimonials-grid {
      grid-template-columns: 1fr;
    }

    .section-header {
      h2 {
        font-size: 2rem;
      }
    }
  }

  .cta-section {
    .cta-content {
      h2 {
        font-size: 2rem;
      }

      .cta-actions {
        flex-direction: column;
        align-items: center;

        .cta-primary,
        .cta-secondary {
          width: 100%;
          max-width: 300px;
        }
      }
    }
  }

  .footer {
    .footer-content {
      grid-template-columns: 1fr;
      gap: 2rem;

      .footer-links {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }
    }

    .footer-bottom {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 1rem;
  }

  .hero {
    .hero-title {
      font-size: 2rem;
    }

    .hero-subtitle {
      font-size: 1.1rem;
    }
  }

  .phone-mockup {
    width: 240px !important;
    height: 480px !important;
  }
}