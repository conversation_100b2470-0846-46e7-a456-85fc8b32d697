import { CommonModule, TitleCasePipe } from '@angular/common';
import { Component, OnDestroy, OnInit, computed, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, combineLatest, takeUntil } from 'rxjs';

import { AttendanceRecord, AttendanceService, LiveAttendance } from '@core/services/attendance.service';
import { AuthService } from '@core/services/auth.service';
import { GeofenceConfig, GeofenceService, GeofenceStatus } from '@core/services/geofence.service';
import { MembersService } from '@core/services/members.service';
import { User } from '@shared/models/user.model';

export interface LiveAttendanceRecord extends Omit<AttendanceRecord, 'member'> {
  member?: User | {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    fullName?: string;
    profilePicture?: string;
  };
  status: 'present' | 'late' | 'absent';
  checkInTimeFormatted: string;
}

export interface AttendanceFilters {
  search: string;
  status: 'all' | 'present' | 'late' | 'absent';
  sortBy: 'name' | 'checkInTime' | 'distance';
  sortOrder: 'asc' | 'desc';
}

@Component({
  selector: 'app-live-attendance',
  standalone: true,
  imports: [CommonModule, FormsModule, TitleCasePipe],
  templateUrl: './live-attendance.component.html',
  styleUrl: './live-attendance.component.scss'
})
export class LiveAttendanceComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private refreshInterval = 15000; // 15 seconds for more real-time feel
  private refreshTimer?: any;

  // Signals for reactive UI
  isLoading = signal<boolean>(true);
  error = signal<string | null>(null);
  searchQuery = signal<string>('');
  selectedStatus = signal<string>('all');
  sortBy = signal<string>('checkInTime');
  sortOrder = signal<string>('desc');
  lastUpdated = signal<Date>(new Date());
  autoRefresh = signal<boolean>(true);

  // Data signals
  liveAttendance = signal<LiveAttendance | null>(null);
  members = signal<User[]>([]);
  currentUser = signal<any>(null);

  // Geofence signals
  geofenceStatus = signal<GeofenceStatus | null>(null);
  isGeofenceActive = signal<boolean>(false);

  // Computed properties
  attendanceRecords = computed(() => {
    const attendance = this.liveAttendance();
    const membersList = this.members();

    if (!attendance || !membersList) return [];

    // Combine attendance records with member data
    const records: LiveAttendanceRecord[] = attendance.recentCheckIns.map(record => {
      // Use member data from the record if available, otherwise find from members list
      const member = record.member || membersList.find(m => m.id === record.memberId);

      // Debug logging for member lookup
      if (!member) {
        console.warn('Member not found for attendance record:', {
          recordId: record.id,
          memberId: record.memberId,
          availableMembers: membersList.map(m => ({ id: m.id, name: m.fullName || `${m.firstName} ${m.lastName}` }))
        });
      }

      const checkInTime = new Date(record.checkInTime);
      const now = new Date();
      const diffMinutes = Math.floor((now.getTime() - checkInTime.getTime()) / (1000 * 60));

      let status: 'present' | 'late' | 'absent' = 'present';
      if (diffMinutes > 30) status = 'late'; // Consider late if checked in more than 30 minutes ago

      return {
        ...record,
        member,
        status,
        checkInTimeFormatted: checkInTime.toLocaleTimeString()
      };
    });

    return this.filterAndSortRecords(records);
  });

  attendanceStats = computed(() => {
    const attendance = this.liveAttendance();
    const records = this.attendanceRecords();

    if (!attendance) return null;

    // Get unique members who checked in (to avoid counting duplicates)
    const uniqueCheckedInMembers = new Set();
    const presentMembers = new Set();
    const lateMembers = new Set();

    records.forEach(record => {
      if (record.member?.id) {
        uniqueCheckedInMembers.add(record.member.id);
        if (record.status === 'present') {
          presentMembers.add(record.member.id);
        } else if (record.status === 'late') {
          lateMembers.add(record.member.id);
        }
      }
    });

    const presentCount = presentMembers.size;
    const lateCount = lateMembers.size;
    const totalCheckedIn = uniqueCheckedInMembers.size;
    const totalMembers = this.members()?.length || 0;

    // Ensure attendance rate doesn't exceed 100%
    const attendanceRate = totalMembers > 0 ? Math.min(100, Math.round(totalCheckedIn / totalMembers * 100)) : 0;

    return {
      totalPresent: presentCount,
      totalLate: lateCount,
      totalCheckedIn,
      totalMembers,
      attendanceRate,
      absentCount: Math.max(0, totalMembers - totalCheckedIn)
    };
  });

  constructor(
    private authService: AuthService,
    private attendanceService: AttendanceService,
    private membersService: MembersService,
    private geofenceService: GeofenceService,
    private router: Router
  ) { }

  ngOnInit(): void {
    // Initialize signals with service data
    this.currentUser.set(this.authService.currentUser());
    this.liveAttendance.set(this.attendanceService.liveAttendance());
    this.members.set(this.membersService.members());

    this.loadInitialData();
    this.startRealTimeUpdates();
    this.initializeGeofence();
  }

  private startRealTimeUpdates(): void {
    if (this.autoRefresh()) {
      this.refreshTimer = setInterval(() => {
        this.refreshData();
      }, this.refreshInterval);
    }
  }

  private stopRealTimeUpdates(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = undefined;
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
    this.stopGeofenceMonitoring();
  }

  private loadInitialData(): void {
    const user = this.currentUser();

    if (!user || !user.churchId) {
      this.error.set('No church associated with your account');
      this.isLoading.set(false);
      return;
    }

    this.isLoading.set(true);
    this.error.set(null);

    // Load members and initial attendance data
    Promise.all([
      this.membersService.getChurchMembers(user.churchId).toPromise(),
      this.attendanceService.getLiveAttendance(user.churchId).toPromise()
    ]).then(([members, liveAttendance]) => {
      // Update signals with loaded data
      this.members.set(members || []);
      this.liveAttendance.set(liveAttendance || null);
      this.lastUpdated.set(new Date());
      this.isLoading.set(false);
      console.log('✅ Live attendance data loaded:', {
        membersCount: members?.length || 0,
        attendanceRecords: liveAttendance?.recentCheckIns?.length || 0
      });
    }).catch(error => {
      console.error('Error loading live attendance data:', error);
      this.error.set('Failed to load attendance data. Please try again.');
      this.isLoading.set(false);
    });
  }

  private filterAndSortRecords(records: LiveAttendanceRecord[]): LiveAttendanceRecord[] {
    let filtered = records;

    // Apply search filter
    const search = this.searchQuery().toLowerCase();
    if (search) {
      filtered = filtered.filter(record =>
        record.member?.firstName.toLowerCase().includes(search) ||
        record.member?.lastName.toLowerCase().includes(search) ||
        record.member?.email.toLowerCase().includes(search)
      );
    }

    // Apply status filter
    const status = this.selectedStatus();
    if (status !== 'all') {
      filtered = filtered.filter(record => record.status === status);
    }

    // Apply sorting
    const sortBy = this.sortBy();
    const sortOrder = this.sortOrder();

    filtered.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'name':
          const nameA = `${a.member?.firstName || ''} ${a.member?.lastName || ''}`;
          const nameB = `${b.member?.firstName || ''} ${b.member?.lastName || ''}`;
          comparison = nameA.localeCompare(nameB);
          break;
        case 'checkInTime':
          comparison = new Date(a.checkInTime).getTime() - new Date(b.checkInTime).getTime();
          break;
        case 'distance':
          comparison = (a.distance || 0) - (b.distance || 0);
          break;
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return filtered;
  }

  // Event handlers
  onSearchChange(query: string): void {
    this.searchQuery.set(query);
  }

  onStatusFilterChange(status: string): void {
    this.selectedStatus.set(status);
  }

  onSortChange(sortBy: string): void {
    if (this.sortBy() === sortBy) {
      // Toggle sort order if same field
      this.sortOrder.set(this.sortOrder() === 'asc' ? 'desc' : 'asc');
    } else {
      this.sortBy.set(sortBy);
      this.sortOrder.set('asc');
    }
  }

  onRefreshData(): void {
    this.loadInitialData();
  }



  onExportData(): void {
    // TODO: Implement export functionality
    console.log('Export data requested');
  }

  // Action methods
  onRefresh(): void {
    this.refreshData();
  }

  onToggleAutoRefresh(): void {
    const newValue = !this.autoRefresh();
    this.autoRefresh.set(newValue);

    if (newValue) {
      this.startRealTimeUpdates();
    } else {
      this.stopRealTimeUpdates();
    }
  }

  private refreshData(): void {
    const user = this.currentUser();
    if (!user?.churchId) return;

    // Don't show loading spinner for background refreshes
    combineLatest([
      this.attendanceService.getLiveAttendance(user.churchId),
      this.membersService.getChurchMembers(user.churchId)
    ]).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: ([liveAttendance, members]: [LiveAttendance, User[]]) => {
        this.liveAttendance.set(liveAttendance);
        this.members.set(members);
        this.lastUpdated.set(new Date());
        this.error.set(null);
      },
      error: (error: any) => {
        console.error('Error refreshing live attendance:', error);
        this.error.set('Failed to refresh attendance data');
      }
    });
  }

  getLastUpdatedText(): string {
    const lastUpdated = this.lastUpdated();
    const now = new Date();
    const diffSeconds = Math.floor((now.getTime() - lastUpdated.getTime()) / 1000);

    if (diffSeconds < 60) return 'Just now';
    if (diffSeconds < 3600) return `${Math.floor(diffSeconds / 60)}m ago`;
    return lastUpdated.toLocaleTimeString();
  }

  // Utility methods
  getStatusColor(status: string): string {
    switch (status) {
      case 'present': return '#38a169';
      case 'late': return '#d69e2e';
      case 'absent': return '#e53e3e';
      default: return '#718096';
    }
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'present': return 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z';
      case 'late': return 'M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z';
      case 'absent': return 'M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z';
      default: return 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z';
    }
  }

  formatDistance(distance?: number): string {
    if (distance === undefined) return 'Unknown';
    return distance < 1000 ? `${Math.round(distance)}m` : `${(distance / 1000).toFixed(1)}km`;
  }

  // Geofence methods
  private initializeGeofence(): void {
    const user = this.currentUser();
    if (!user?.churchId) return;

    // For now, use default church location (you can get this from church settings)
    const geofenceConfig: GeofenceConfig = {
      churchLatitude: 5.6037, // Default to Accra, Ghana - should come from church settings
      churchLongitude: -0.1870,
      radius: 100, // 100 meters - should come from church settings
      autoCheckIn: true
    };

    console.log('🌍 Initializing geofence monitoring for live attendance');

    // Start monitoring geofence
    this.geofenceService.startMonitoring(geofenceConfig).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (status) => {
        this.geofenceStatus.set(status);
        this.isGeofenceActive.set(this.geofenceService.isCurrentlyMonitoring());

        // If someone just checked in via geofence, refresh the attendance data
        if (status.isInside) {
          setTimeout(() => {
            this.refreshData();
          }, 2000); // Wait 2 seconds for the check-in to be processed
        }
      },
      error: (error) => {
        console.error('❌ Geofence monitoring error:', error);
        this.error.set(`Geofence error: ${error.message}`);
      }
    });
  }

  private stopGeofenceMonitoring(): void {
    this.geofenceService.stopMonitoring();
    this.isGeofenceActive.set(false);
  }


}
