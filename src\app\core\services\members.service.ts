import { Injectable, signal } from '@angular/core';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { Firestore, collection, doc, addDoc, setDoc, query, where, getDocs, getDoc, deleteDoc, Timestamp } from '@angular/fire/firestore';
import { Auth, createUserWithEmailAndPassword, updateProfile, getAuth } from '@angular/fire/auth';
import { initializeApp, FirebaseApp } from '@angular/fire/app';

import { User, CreateUserRequest, UpdateUserRequest } from '@shared/models/user.model';
import { environment } from '@environments/environment';

export interface Member {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: string;
  address?: string;
  profilePicture?: string;
  role: string;
  status: string;
  churchId: string;
  lastLoginAt?: string;
  isLocationTrackingEnabled: boolean;
  isNotificationsEnabled: boolean;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  createdAt: string;
  updatedAt: string;
  fullName: string;
}

export interface MemberStats {
  totalMembers: number;
  activeMembers: number;
  newMembersThisMonth: number;
  membersByRole: {
    [role: string]: number;
  };
  membersByStatus: {
    [status: string]: number;
  };
  recentlyActive: User[];
}

export interface CreateMemberRequest {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: string;
  address?: string;
  churchId: string;
  role?: string;
  password: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
}

export interface UpdateMemberRequest {
  firstName?: string;
  lastName?: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: string;
  address?: string;
  isLocationTrackingEnabled?: boolean;
  isNotificationsEnabled?: boolean;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
}

export interface UpdateMemberRoleRequest {
  role: string;
}

export interface UpdateMemberStatusRequest {
  status: string;
  reason?: string;
}

export interface JoinChurchRequest {
  churchId: string;
  inviteCode?: string;
}

@Injectable({
  providedIn: 'root'
})
export class MembersService {
  // Firebase-based service - no API URL needed

  // Signals for reactive UI
  members = signal<User[]>([]);
  memberStats = signal<MemberStats | null>(null);
  isLoading = signal<boolean>(false);

  // BehaviorSubjects for components that need observables
  private membersSubject = new BehaviorSubject<User[]>([]);
  private memberStatsSubject = new BehaviorSubject<MemberStats | null>(null);

  // Secondary Firebase app for creating users without affecting current auth state
  private secondaryApp: FirebaseApp | null = null;

  constructor(
    private firestore: Firestore,
    private auth: Auth
  ) {
    this.initializeSecondaryApp();
  }

  /**
   * Initialize secondary Firebase app for user creation
   */
  private initializeSecondaryApp(): void {
    try {
      this.secondaryApp = initializeApp(environment.firebase, 'secondary');
      console.log('✅ Secondary Firebase app initialized for user creation');
    } catch (error) {
      console.error('❌ Error initializing secondary Firebase app:', error);
    }
  }



  /**
   * Create a new member with Firebase Auth and Firestore using secondary app
   */
  createMember(memberData: CreateUserRequest): Observable<{ message: string; member: User }> {
    return new Observable(observer => {
      // Validate that password is provided for member creation
      if (!memberData.password) {
        observer.error(new Error('Password is required for creating new members'));
        return;
      }

      // Validate that secondary app is initialized
      if (!this.secondaryApp) {
        observer.error(new Error('Secondary Firebase app not initialized'));
        return;
      }

      // Step 1: Create Firebase Auth user using secondary app (this won't affect current user)
      const secondaryAuth = getAuth(this.secondaryApp);
      createUserWithEmailAndPassword(secondaryAuth, memberData.email, memberData.password)
        .then(async (userCredential) => {
          const authUser = userCredential.user;
          console.log('✅ Firebase Auth user created with secondary app:', authUser.uid);

          // Step 2: Update the user's display name
          await updateProfile(authUser, {
            displayName: `${memberData.firstName} ${memberData.lastName}`
          });

          // Step 3: Create Firestore document with the Auth UID
          const usersCollection = collection(this.firestore, 'users');
          const docData: any = {
            id: authUser.uid, // Use Firebase Auth UID as document ID
            email: memberData.email,
            firstName: memberData.firstName,
            lastName: memberData.lastName,
            fullName: `${memberData.firstName} ${memberData.lastName}`,
            churchId: memberData.churchId || null,
            role: memberData.role,
            status: memberData.status || 'active',
            createdAt: Timestamp.now(),
            updatedAt: Timestamp.now()
          };

          // Add optional fields only if they have values
          console.log('Processing member data:', memberData);

          if (memberData.phone && memberData.phone.trim()) {
            docData.phone = memberData.phone.trim();
          }
          if (memberData.dateOfBirth) docData.dateOfBirth = memberData.dateOfBirth;
          if (memberData.gender) docData.gender = memberData.gender;
          if (memberData.address && memberData.address.trim()) docData.address = memberData.address.trim();
          if (memberData.emergencyContactName && memberData.emergencyContactName.trim()) {
            docData.emergencyContactName = memberData.emergencyContactName.trim();
          }
          if (memberData.emergencyContactPhone && memberData.emergencyContactPhone.trim()) {
            docData.emergencyContactPhone = memberData.emergencyContactPhone.trim();
          }
          if (memberData.profilePicture) docData.profilePicture = memberData.profilePicture;

          console.log('Final docData being saved to Firestore:', docData);

          // Use the Auth UID as the document ID
          const userDocRef = doc(this.firestore, 'users', authUser.uid);

          // Step 4: Save to Firestore with Auth UID as document ID
          await setDoc(userDocRef, docData);
          console.log('✅ Member document created with Auth UID as document ID:', authUser.uid);

          const user: User = {
            id: authUser.uid,
            email: docData.email,
            firstName: docData.firstName,
            lastName: docData.lastName,
            fullName: docData.fullName,
            churchId: docData.churchId,
            role: docData.role,
            status: docData.status,
            phone: docData.phone,
            dateOfBirth: docData.dateOfBirth,
            gender: docData.gender,
            address: docData.address,
            emergencyContactName: docData.emergencyContactName,
            emergencyContactPhone: docData.emergencyContactPhone,
            profilePicture: docData.profilePicture,
            createdAt: docData.createdAt,
            updatedAt: docData.updatedAt
          };

          console.log('✅ Member created successfully with Auth UID:', authUser.uid);

          // Note: Using secondary Firebase app prevents the current admin from being logged out
          // The new member is created but not automatically signed in to the main app
          console.log('✅ Admin remains logged in, new member created successfully');

          observer.next({ message: 'Member created successfully with login credentials', member: user });
          observer.complete();
        })
        .catch(error => {
          console.error('❌ Error creating member:', error);

          // Provide user-friendly error messages
          let errorMessage = 'Failed to create member';
          if (error.code === 'auth/email-already-in-use') {
            errorMessage = 'Email address is already registered';
          } else if (error.code === 'auth/weak-password') {
            errorMessage = 'Password is too weak. Please use at least 8 characters';
          } else if (error.code === 'auth/invalid-email') {
            errorMessage = 'Invalid email address format';
          }

          observer.error(new Error(errorMessage));
        });
    });
  }

  /**
   * Get current user profile
   */
  getProfile(): Observable<User> {
    // TODO: Get current user ID from auth service
    return of({} as User);
  }

  /**
   * Update current user profile
   */
  updateProfile(updateData: UpdateUserRequest): Observable<{ message: string; member: User }> {
    // TODO: Implement Firebase-based profile update
    return of({ message: 'Profile updated successfully', member: {} as User });
  }

  /**
   * Join a church
   */
  joinChurch(joinData: any): Observable<{ message: string; member: User }> {
    // TODO: Implement Firebase-based church joining
    return of({ message: 'Joined church successfully', member: {} as User });
  }

  /**
   * Get all members of a church
   */
  getChurchMembers(churchId: string, includeInactive: boolean = false): Observable<User[]> {
    this.isLoading.set(true);

    const usersCollection = collection(this.firestore, 'users');
    const membersQuery = query(
      usersCollection,
      where('churchId', '==', churchId)
    );

    return new Observable(observer => {
      getDocs(membersQuery).then(querySnapshot => {
        const allMembers: User[] = [];
        querySnapshot.forEach(doc => {
          const userData = doc.data() as User;
          console.log('Raw member data from Firebase:', userData);
          console.log('Phone field value:', userData.phone);
          allMembers.push({
            ...userData,
            id: doc.id
          });
        });

        // Filter members based on status
        let filteredMembers = allMembers;
        if (!includeInactive) {
          // Only include active members and those without status (for backward compatibility)
          filteredMembers = allMembers.filter(member =>
            !member.status || member.status === 'active'
          );
        }

        console.log('All members retrieved:', allMembers.length);
        console.log('Filtered members:', filteredMembers.length);
        this.members.set(filteredMembers);
        this.membersSubject.next(filteredMembers);
        this.isLoading.set(false);

        observer.next(filteredMembers);
        observer.complete();
      }).catch(error => {
        this.isLoading.set(false);
        observer.error(error);
      });
    });
  }

  /**
   * Get member statistics for a church
   */
  getChurchMemberStats(churchId: string): Observable<MemberStats> {
    this.isLoading.set(true);

    const usersCollection = collection(this.firestore, 'users');
    const membersQuery = query(
      usersCollection,
      where('churchId', '==', churchId)
    );

    return new Observable(observer => {
      getDocs(membersQuery).then(querySnapshot => {
        const allMembers: User[] = [];
        querySnapshot.forEach(doc => {
          const userData = doc.data() as User;
          allMembers.push({
            ...userData,
            id: doc.id
          });
        });

        // Calculate statistics
        const activeMembers = allMembers.filter(member =>
          !member.status || member.status === 'active'
        );

        const totalMembers = activeMembers.length;

        // Calculate new members this month
        const oneMonthAgo = new Date();
        oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
        const newMembersThisMonth = activeMembers.filter(member =>
          member.createdAt && member.createdAt.toDate() >= oneMonthAgo
        ).length;

        // Group by role
        const membersByRole: { [key: string]: number } = {};
        activeMembers.forEach(member => {
          membersByRole[member.role] = (membersByRole[member.role] || 0) + 1;
        });

        // Group by status
        const membersByStatus: { [key: string]: number } = {};
        allMembers.forEach(member => {
          const status = member.status || 'active';
          membersByStatus[status] = (membersByStatus[status] || 0) + 1;
        });

        // Get recently active members (those with recent lastLoginAt)
        const recentlyActive = activeMembers
          .filter(member => member.lastLoginAt)
          .sort((a, b) => {
            const aTime = a.lastLoginAt?.toDate().getTime() || 0;
            const bTime = b.lastLoginAt?.toDate().getTime() || 0;
            return bTime - aTime;
          })
          .slice(0, 5);

        const stats: MemberStats = {
          totalMembers,
          activeMembers: totalMembers,
          newMembersThisMonth,
          membersByRole,
          membersByStatus,
          recentlyActive
        };

        console.log('Member stats calculated:', stats);
        this.memberStats.set(stats);
        this.memberStatsSubject.next(stats);
        this.isLoading.set(false);

        observer.next(stats);
        observer.complete();
      }).catch(error => {
        console.error('Error calculating member stats:', error);
        this.isLoading.set(false);
        observer.error(error);
      });
    });
  }

  /**
   * Search members by name or email
   */
  searchMembers(churchId: string, query: string): Observable<User[]> {
    if (!query || query.trim().length < 2) {
      return of([]);
    }

    // TODO: Implement Firebase-based member search
    return of([]);
  }

  /**
   * Get recently active members
   */
  getRecentlyActiveMembers(churchId: string, days: number = 7): Observable<User[]> {
    // TODO: Implement Firebase-based recently active members
    return of([]);
  }

  /**
   * Get member by ID
   */
  getMember(id: string): Observable<User> {
    const userRef = doc(this.firestore, 'users', id);

    return new Observable(observer => {
      getDoc(userRef).then(docSnap => {
        if (docSnap.exists()) {
          const userData = docSnap.data() as User;
          observer.next({ ...userData, id: docSnap.id });
        } else {
          observer.error(new Error('User not found'));
        }
        observer.complete();
      }).catch(error => {
        observer.error(error);
      });
    });
  }

  /**
   * Update member role (admin only)
   */
  updateMemberRole(id: string, roleData: any): Observable<{ message: string; member: User }> {
    // TODO: Implement Firebase-based role update
    return of({ message: 'Role updated successfully', member: {} as User });
  }

  /**
   * Update member status (admin only)
   */
  updateMemberStatus(id: string, statusData: any): Observable<{ message: string; member: User }> {
    // TODO: Implement Firebase-based status update
    return of({ message: 'Status updated successfully', member: {} as User });
  }

  /**
   * Remove member (set to inactive)
   */
  removeMember(id: string): Observable<{ message: string }> {
    // TODO: Implement Firebase-based member removal
    return of({ message: 'Member removed successfully' });
  }

  /**
   * Refresh church members data
   */
  refreshChurchMembers(churchId: string, includeInactive: boolean = false): void {
    this.getChurchMembers(churchId, includeInactive).subscribe();
  }

  /**
   * Refresh member statistics
   */
  refreshMemberStats(churchId: string): void {
    this.getChurchMemberStats(churchId).subscribe();
  }

  /**
   * Delete a member
   */
  deleteMember(memberId: string): Observable<void> {
    this.isLoading.set(true);

    const userRef = doc(this.firestore, 'users', memberId);

    return new Observable(observer => {
      deleteDoc(userRef).then(() => {
        console.log('Member deleted successfully:', memberId);
        this.isLoading.set(false);
        observer.next();
        observer.complete();
      }).catch(error => {
        console.error('Error deleting member:', error);
        this.isLoading.set(false);
        observer.error(error);
      });
    });
  }

  // Observable getters for components that need them
  get members$(): Observable<User[]> {
    return this.membersSubject.asObservable();
  }

  get memberStats$(): Observable<MemberStats | null> {
    return this.memberStatsSubject.asObservable();
  }
}
