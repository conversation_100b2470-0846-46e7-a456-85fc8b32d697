<div class="welcome-container">
  @if (isLoading()) {
  <div class="loading-state">
    <div class="loading-spinner"></div>
    <p>Loading your church information...</p>
  </div>
  } @else {
  <div class="welcome-card">
    <!-- Header -->
    <div class="header">
      <div class="church-info">
        @if (church()) {
        <h1>{{ church()!.name }}</h1>
        <p class="church-address">{{ church()!.address }}</p>
        }
        @if (user()) {
        <p class="admin-welcome">Welcome, {{ user()!.firstName }} {{ user()!.lastName }}!</p>
        }
      </div>
    </div>

    <!-- Progress Bar -->
    <div class="progress-container">
      <div class="progress-bar">
        <div class="progress-fill" [style.width.%]="progressPercentage"></div>
      </div>
      <span class="progress-text">Step {{ currentStep() }} of {{ totalSteps }}</span>
    </div>

    <!-- Step Content -->
    <div class="step-content">
      <div class="step-icon">{{ currentStepData.icon }}</div>
      <h2>{{ currentStepData.title }}</h2>
      <p>{{ currentStepData.description }}</p>

      <!-- Step-specific content -->
      @switch (currentStep()) {
      @case (1) {
      <div class="step-details">
        <div class="feature-list">
          <div class="feature-item">
            <span class="feature-icon">✅</span>
            <span>Real-time attendance tracking</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">📱</span>
            <span>Mobile check-in for members</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">📈</span>
            <span>Detailed attendance reports</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">👨‍👩‍👧‍👦</span>
            <span>Member management system</span>
          </div>
        </div>
      </div>
      }
      @case (2) {
      <div class="step-details">
        <div class="info-box">
          <h4>Your Church Code</h4>
          @if (church()) {
          <div class="church-code">{{ church()!.id }}</div>
          <p class="code-description">
            Share this code with your members so they can join your church on the platform.
          </p>
          }
        </div>
        <div class="action-buttons">
          <button class="btn btn-outline" (click)="goToMembers()">
            Manage Members
          </button>
        </div>
      </div>
      }
      @case (3) {
      <div class="step-details">
        <div class="settings-preview">
          @if (church()) {
          <div class="setting-item">
            <span class="setting-label">Service Types:</span>
            <span class="setting-value">{{ church()!.serviceTypes.join(', ') }}</span>
          </div>
          <div class="setting-item">
            <span class="setting-label">Geofence Radius:</span>
            <span class="setting-value">{{ church()!.settings.geofenceRadius }}m</span>
          </div>
          <div class="setting-item">
            <span class="setting-label">Self Registration:</span>
            <span class="setting-value">{{ church()!.settings.allowSelfRegistration ? 'Enabled' : 'Disabled' }}</span>
          </div>
          }
        </div>
        <div class="action-buttons">
          <button class="btn btn-outline" (click)="goToSettings()">
            Configure Settings
          </button>
        </div>
      </div>
      }
      @case (4) {
      <div class="step-details">
        <div class="completion-message">
          <h4>🎊 Congratulations!</h4>
          <p>Your church is now fully set up and ready to use. You can start tracking attendance right away!</p>
        </div>
        <div class="quick-actions">
          <h5>Quick Actions:</h5>
          <div class="action-grid">
            <button class="action-card" (click)="goToMembers()">
              <span class="action-icon">👥</span>
              <span class="action-title">Add Members</span>
            </button>
            <button class="action-card" (click)="goToSettings()">
              <span class="action-icon">⚙️</span>
              <span class="action-title">Settings</span>
            </button>
          </div>
        </div>
      </div>
      }
      }
    </div>

    <!-- Navigation -->
    <div class="navigation">
      @if (currentStep() > 1) {
      <button class="btn btn-secondary" (click)="previousStep()">
        Previous
      </button>
      }

      <div class="nav-spacer"></div>

      @if (currentStep() < totalSteps) { <button class="btn btn-primary" (click)="nextStep()">
        Next
        </button>
        } @else {
        <button class="btn btn-primary" (click)="goToDashboard()">
          Go to Dashboard
        </button>
        }
    </div>

    <!-- Skip Option -->
    @if (currentStep() < totalSteps) { <div class="skip-option">
      <button class="btn-link" (click)="goToDashboard()">
        Skip tour and go to dashboard
      </button>
  </div>
  }
</div>
}
</div>