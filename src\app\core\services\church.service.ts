import { Injectable, signal } from '@angular/core';
import { Firestore, collection, doc, addDoc, getDoc, getDocs, updateDoc, deleteDoc, query, where, orderBy, Timestamp } from '@angular/fire/firestore';
import { Observable, BehaviorSubject, from } from 'rxjs';
import { map, tap, catchError } from 'rxjs/operators';

import { Church, CreateChurchRequest, UpdateChurchRequest, ServiceTime } from '@shared/models/church.model';

@Injectable({
  providedIn: 'root'
})
export class ChurchService {
  // Signals for reactive UI
  church = signal<Church | null>(null);
  isLoading = signal<boolean>(false);
  error = signal<string | null>(null);

  // BehaviorSubjects for components that need observables
  private churchSubject = new BehaviorSubject<Church | null>(null);

  constructor(private firestore: Firestore) { }

  /**
   * Get all churches
   */
  getChurches(): Observable<Church[]> {
    this.isLoading.set(true);
    this.error.set(null);

    const churchesRef = collection(this.firestore, 'churches');
    const q = query(churchesRef, orderBy('name'));

    return from(getDocs(q)).pipe(
      map(snapshot => {
        const churches = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        } as Church));
        this.isLoading.set(false);
        return churches;
      }),
      catchError(error => {
        this.error.set('Failed to load churches');
        this.isLoading.set(false);
        throw error;
      })
    );
  }

  /**
   * Get church by ID
   */
  getChurch(churchId: string): Observable<Church | null> {
    console.log('ChurchService: Getting church with ID:', churchId);
    this.isLoading.set(true);
    this.error.set(null);

    const churchRef = doc(this.firestore, 'churches', churchId);

    return from(getDoc(churchRef)).pipe(
      map(docSnap => {
        console.log('ChurchService: Document exists:', docSnap.exists());
        if (docSnap.exists()) {
          const rawData = docSnap.data();
          console.log('ChurchService: Raw church data:', rawData);
          const church = { id: docSnap.id, ...rawData } as Church;
          console.log('ChurchService: Processed church data:', church);
          this.church.set(church);
          this.churchSubject.next(church);
          this.isLoading.set(false);
          return church;
        } else {
          console.log('ChurchService: Church document does not exist');
          this.isLoading.set(false);
          return null;
        }
      }),
      catchError(error => {
        console.error('ChurchService: Error getting church:', error);
        this.error.set('Failed to load church information');
        this.isLoading.set(false);
        throw error;
      })
    );
  }

  /**
   * Create a new church
   */
  createChurch(churchData: CreateChurchRequest): Observable<string> {
    this.isLoading.set(true);
    this.error.set(null);

    console.log('Creating church with data:', churchData);

    const churchesRef = collection(this.firestore, 'churches');
    const newChurch = {
      ...churchData,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    };

    return from(addDoc(churchesRef, newChurch)).pipe(
      map(docRef => {
        console.log('Church created successfully with ID:', docRef.id);
        this.isLoading.set(false);
        return docRef.id;
      }),
      catchError(error => {
        console.error('Failed to create church:', error);
        this.error.set('Failed to create church');
        this.isLoading.set(false);
        throw error;
      })
    );
  }

  /**
   * Update church information
   */
  updateChurch(churchId: string, updateData: UpdateChurchRequest): Observable<void> {
    this.isLoading.set(true);
    this.error.set(null);

    const churchRef = doc(this.firestore, 'churches', churchId);
    const updatedData = {
      ...updateData,
      updatedAt: Timestamp.now()
    };

    return from(updateDoc(churchRef, updatedData)).pipe(
      tap(() => {
        this.isLoading.set(false);
        // Refresh the church data
        this.getChurch(churchId).subscribe();
      }),
      catchError(error => {
        this.error.set('Failed to update church information');
        this.isLoading.set(false);
        throw error;
      })
    );
  }

  /**
   * Delete a church
   */
  deleteChurch(churchId: string): Observable<void> {
    this.isLoading.set(true);
    this.error.set(null);

    const churchRef = doc(this.firestore, 'churches', churchId);

    return from(deleteDoc(churchRef)).pipe(
      tap(() => {
        this.isLoading.set(false);
        this.church.set(null);
        this.churchSubject.next(null);
      }),
      catchError(error => {
        this.error.set('Failed to delete church');
        this.isLoading.set(false);
        throw error;
      })
    );
  }

  /**
   * Find churches by admin ID
   */
  getChurchesByAdmin(adminId: string): Observable<Church[]> {
    this.isLoading.set(true);
    this.error.set(null);

    const churchesRef = collection(this.firestore, 'churches');
    const q = query(churchesRef, where('adminIds', 'array-contains', adminId));

    return from(getDocs(q)).pipe(
      map(snapshot => {
        const churches = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        } as Church));
        this.isLoading.set(false);
        return churches;
      }),
      catchError(error => {
        this.error.set('Failed to load churches');
        this.isLoading.set(false);
        throw error;
      })
    );
  }

  /**
   * Refresh church data
   */
  refreshChurch(churchId: string): void {
    this.getChurch(churchId).subscribe();
  }

  /**
   * Clear church data
   */
  clearChurch(): void {
    this.church.set(null);
    this.churchSubject.next(null);
    this.error.set(null);
  }

  // Observable getters for components that need them
  get church$(): Observable<Church | null> {
    return this.churchSubject.asObservable();
  }

  /**
   * Get default service times template
   */
  getDefaultServiceTimes(): ServiceTime[] {
    return [
      {
        name: 'Sunday Morning Service',
        day: 'sunday',
        startTime: '09:00',
        endTime: '11:00'
      },
      {
        name: 'Wednesday Bible Study',
        day: 'wednesday',
        startTime: '18:00',
        endTime: '20:00'
      }
    ];
  }

  /**
   * Validate coordinates
   */
  isValidCoordinates(latitude: number, longitude: number): boolean {
    return latitude >= -90 && latitude <= 90 && longitude >= -180 && longitude <= 180;
  }
}
