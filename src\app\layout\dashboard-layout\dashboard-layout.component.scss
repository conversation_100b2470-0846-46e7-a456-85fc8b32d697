.dashboard-layout {
  display: flex;
  height: 100vh;
  background: #f7fafc;
}

.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
  display: none;

  @media (max-width: 768px) {
    display: block;
  }
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: 280px;
  transition: margin-left 0.3s ease;

  &.sidebar-collapsed {
    margin-left: 80px;
  }

  @media (max-width: 768px) {
    margin-left: 0;

    &.sidebar-collapsed {
      margin-left: 0;
    }
  }
}

.page-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  background: #f7fafc;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}
