import { Injectable, signal, inject } from '@angular/core';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { Auth, signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut, authState, User as FirebaseUser } from '@angular/fire/auth';
import { Firestore, doc, setDoc, getDoc, Timestamp } from '@angular/fire/firestore';
import { Observable, BehaviorSubject, from, of } from 'rxjs';
import { map, switchMap, tap, catchError } from 'rxjs/operators';

import { User, CreateUserRequest } from '@shared/models/user.model';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);

  // Signals for reactive UI
  currentUser = signal<User | null>(null);
  isAuthenticated = signal<boolean>(false);
  isLoading = signal<boolean>(false);

  // Observable for user data
  user$: Observable<User | null>;

  constructor(
    private auth: Auth,
    private firestore: Firestore,
    private router: Router,
    private http: HttpClient
  ) {
    this.user$ = authState(this.auth).pipe(
      switchMap(user => {
        if (user) {
          return this.getUserDocument(user.uid);
        } else {
          return of(null);
        }
      }),
      tap(user => {
        this.currentUser.set(user);
        this.isAuthenticated.set(!!user);
        this.currentUserSubject.next(user);
        this.isAuthenticatedSubject.next(!!user);
      })
    );

    // Subscribe to user changes
    this.user$.subscribe();
  }

  /**
   * Sign in with email and password
   */
  async signIn(email: string, password: string): Promise<void> {
    this.isLoading.set(true);
    try {
      const credential = await signInWithEmailAndPassword(this.auth, email, password);

      // Update lastLoginAt field
      if (credential.user) {
        await this.updateLastLogin(credential.user.uid);

        // Initialize FCM for notifications (async, don't wait)
        this.initializeFCMForUser(credential.user.uid);
      }

      // User data will be automatically loaded through the authState observable
    } catch (error: any) {
      console.error('Sign in error:', error);
      throw new Error(error.message || 'Sign in failed');
    } finally {
      this.isLoading.set(false);
    }
  }

  /**
   * Sign up with email and password
   */
  async signUp(userData: CreateUserRequest, password: string): Promise<void> {
    this.isLoading.set(true);
    try {
      const credential = await createUserWithEmailAndPassword(this.auth, userData.email, password);

      if (credential.user) {
        const userDoc: User = {
          id: credential.user.uid,
          email: userData.email,
          firstName: userData.firstName,
          lastName: userData.lastName,
          churchId: userData.churchId || null,
          role: userData.role,
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now()
        };

        // Add optional fields if provided
        if (userData.profilePicture) {
          userDoc.profilePicture = userData.profilePicture;
        }
        if (userData.phone) {
          userDoc.phone = userData.phone;
        }
        if (userData.dateOfBirth) {
          userDoc.dateOfBirth = userData.dateOfBirth;
        }
        if (userData.gender) {
          userDoc.gender = userData.gender;
        }
        if (userData.address) {
          userDoc.address = userData.address;
        }
        if (userData.emergencyContactName) {
          userDoc.emergencyContactName = userData.emergencyContactName;
        }
        if (userData.emergencyContactPhone) {
          userDoc.emergencyContactPhone = userData.emergencyContactPhone;
        }
        if (userData.status) {
          userDoc.status = userData.status;
        }

        await this.updateUserData(userDoc);
      }
    } catch (error: any) {
      console.error('Sign up error:', error);
      throw new Error(error.message || 'Sign up failed');
    } finally {
      this.isLoading.set(false);
    }
  }

  /**
   * Sign out user
   */
  async signOut(): Promise<void> {
    try {
      await signOut(this.auth);
      this.router.navigate(['/login']);
    } catch (error: any) {
      console.error('Sign out error:', error);
      throw new Error(error.message || 'Sign out failed');
    }
  }

  /**
   * Update user church association
   */
  async updateUserChurch(churchId: string): Promise<void> {
    const currentUser = this.currentUser();
    if (currentUser) {
      const updatedUser = {
        ...currentUser,
        churchId,
        updatedAt: Timestamp.now()
      };
      await this.updateUserData(updatedUser);
    }
  }

  /**
   * Check if user is authenticated
   */
  isUserAuthenticated(): boolean {
    return this.isAuthenticated();
  }

  /**
   * Get user role
   */
  getUserRole(): string | null {
    const user = this.currentUser();
    return user ? user.role : null;
  }

  /**
   * Check if user has specific role
   */
  hasRole(role: 'super_admin' | 'admin' | 'pastor' | 'member'): boolean {
    const userRole = this.getUserRole();
    return userRole === role;
  }

  /**
   * Check if user is admin (admin or pastor)
   */
  isAdmin(): boolean {
    return this.hasRole('admin') || this.hasRole('pastor') || this.hasRole('super_admin');
  }

  /**
   * Check if user is super admin
   */
  isSuperAdmin(): boolean {
    return this.hasRole('super_admin');
  }

  /**
   * Check if user is church admin (admin or pastor)
   */
  isChurchAdmin(): boolean {
    return this.hasRole('admin') || this.hasRole('pastor');
  }

  // Private helper methods
  private async getUserDocument(uid: string): Promise<User | null> {
    try {
      console.log('🔍 Fetching user document for UID:', uid);
      const userDoc = await getDoc(doc(this.firestore, 'users', uid));
      if (userDoc.exists()) {
        const userData = { id: uid, ...userDoc.data() } as User;
        console.log('✅ User document found:', userData);
        console.log('🏛️ User churchId:', userData.churchId);
        return userData;
      } else {
        console.log('❌ User document not found for UID:', uid);
        return null;
      }
    } catch (error) {
      console.error('❌ Error getting user document:', error);
      return null;
    }
  }

  private async updateUserData(user: User): Promise<void> {
    try {
      console.log('Updating user data:', user);
      const userRef = doc(this.firestore, 'users', user.id);
      const data = {
        ...user,
        updatedAt: Timestamp.now()
      };
      await setDoc(userRef, data, { merge: true });
      console.log('User data updated successfully');
    } catch (error) {
      console.error('Error updating user data:', error);
      throw error;
    }
  }

  /**
   * Update last login timestamp for a user
   */
  private async updateLastLogin(userId: string): Promise<void> {
    try {
      console.log('🔄 Updating last login for user:', userId);
      const userRef = doc(this.firestore, 'users', userId);
      const updateData = {
        lastLoginAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };
      console.log('📝 Update data:', updateData);

      await setDoc(userRef, updateData, { merge: true });
      console.log('✅ Last login updated successfully at:', new Date().toISOString());

      // Force refresh user data to reflect the change
      setTimeout(async () => {
        try {
          const user = await this.getUserDocument(userId);
          console.log('🔄 Refreshed user data after login update:', user);
          if (user) {
            this.currentUser.set(user);
            this.currentUserSubject.next(user);
          }
        } catch (error) {
          console.error('❌ Error refreshing user data:', error);
        }
      }, 1000); // Wait 1 second for Firestore to propagate

    } catch (error) {
      console.error('❌ Error updating last login:', error);
      // Don't throw error as this is not critical for login process
    }
  }

  // Observable getters for components that need them
  get currentUser$(): Observable<User | null> {
    return this.currentUserSubject.asObservable();
  }

  get isAuthenticated$(): Observable<boolean> {
    return this.isAuthenticatedSubject.asObservable();
  }

  /**
   * Initialize FCM for the user (async, non-blocking)
   */
  private async initializeFCMForUser(userId: string): Promise<void> {
    try {
      // Get user data to find churchId
      const user = await this.getUserDocument(userId);
      if (user && user.churchId) {
        console.log('🔔 Initializing FCM for user:', userId, 'church:', user.churchId);

        // Use setTimeout to avoid circular dependency issues
        setTimeout(async () => {
          try {
            // Dynamically import and initialize FCM
            const { NotificationsService } = await import('./notifications.service');
            const { FCMService } = await import('./fcm.service');
            const { getMessaging } = await import('@angular/fire/messaging');

            const messaging = getMessaging();
            const fcmService = new FCMService(messaging, this.firestore, this.http);
            const notificationsService = new NotificationsService(this.firestore, fcmService, this.auth);

            // Initialize FCM (this will request permission and save token)
            if (user.churchId) {
              const success = await notificationsService.initializeFCM(userId, user.churchId);
              if (success) {
                console.log('✅ FCM initialized successfully for user');
              } else {
                console.log('⚠️ FCM initialization failed or permission denied');
              }
            } else {
              console.log('⚠️ User has no churchId, skipping FCM initialization');
            }
          } catch (error) {
            console.error('❌ Error in FCM initialization:', error);
          }
        }, 1000);
      }
    } catch (error) {
      console.error('❌ Error initializing FCM:', error);
      // Don't throw - this is not critical for login
    }
  }
}
