.settings-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  min-height: calc(100vh - 4rem);
}

// Header Section
.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;

  .header-content {
    flex: 1;

    .page-title {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      font-size: 2rem;
      font-weight: 700;
      color: #1a202c;
      margin: 0 0 0.5rem 0;

      mat-icon {
        font-size: 2rem;
        width: 2rem;
        height: 2rem;
        color: #667eea;
      }
    }

    .page-subtitle {
      color: #718096;
      font-size: 1rem;
      margin: 0;
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;

    .header-actions {
      justify-content: flex-end;
    }
  }
}

// Loading State
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  gap: 1rem;

  p {
    color: #718096;
    margin: 0;
  }
}

// Empty State
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  text-align: center;

  mat-icon {
    font-size: 4rem;
    width: 4rem;
    height: 4rem;
    color: #cbd5e0;
    margin-bottom: 1rem;
  }

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #4a5568;
    margin: 0 0 0.5rem 0;
  }

  p {
    color: #718096;
    margin: 0;
  }
}

// Settings Card
.settings-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  .settings-tabs {
    ::ng-deep {
      .mat-mdc-tab-header {
        border-bottom: 1px solid #e2e8f0;
      }

      .mat-mdc-tab-label {
        font-weight: 500;
        opacity: 0.7;
        transition: all 0.2s ease;

        &.mdc-tab--active {
          opacity: 1;
        }
      }

      .mat-mdc-tab-body-wrapper {
        padding: 0;
      }
    }
  }
}

// Tab Content
.tab-content {
  padding: 2rem;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

// Form Sections
.form-section {
  margin-bottom: 2rem;

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1a202c;
    margin: 0 0 1rem 0;
  }

  .section-description {
    color: #718096;
    font-size: 0.875rem;
    margin: 0 0 1rem 0;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    h3 {
      margin: 0;
    }
  }
}

// Form Grid
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;

  .full-width {
    grid-column: 1 / -1;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

// Form Actions
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;

  @media (max-width: 768px) {
    flex-direction: column-reverse;
    gap: 0.5rem;
  }
}

// Service Times Specific Styles
.service-times-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  .service-time-card {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    transition: box-shadow 0.2s ease;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .service-time-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;

      h4 {
        font-size: 1rem;
        font-weight: 600;
        color: #4a5568;
        margin: 0;
      }

      .remove-btn {
        color: #e53e3e;

        &:hover {
          background-color: rgba(229, 62, 62, 0.1);
        }
      }
    }

    .service-time-form {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;

      .time-fields {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        grid-column: 1 / -1;
      }

      @media (max-width: 768px) {
        grid-template-columns: 1fr;

        .time-fields {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}

// Slider Field
.slider-field {
  width: 100%;
  max-width: 400px;

  ::ng-deep {
    .mat-mdc-form-field-infix {
      padding: 1rem 0;
    }

    .mat-mdc-slider {
      width: 100%;
    }
  }
}

// Material Design Overrides
::ng-deep {
  .mat-mdc-form-field {
    width: 100%;
  }

  .mat-mdc-card {
    border-radius: 12px;
  }

  .mat-mdc-tab-group {
    .mat-mdc-tab-header {
      .mat-mdc-tab-label-container {
        .mat-mdc-tab-label {
          font-weight: 500;
          text-transform: none;
        }
      }
    }
  }

  .success-snackbar {
    background-color: #48bb78;
    color: white;
  }

  .error-snackbar {
    background-color: #f56565;
    color: white;
  }

  .mat-mdc-spinner {
    margin-right: 0.5rem;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .settings-container {
    padding: 1rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .service-time-form {
    grid-template-columns: 1fr;
  }
}

// Location & Branding Tab Styles
.geofence-section {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;

  h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
  }
}

// Custom styles for location picker and logo upload components
app-location-picker,
app-logo-upload {
  display: block;
  width: 100%;
}

// Slider field styles
.slider-field {
  margin-bottom: 1rem;

  .slider-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  mat-slider {
    width: 100%;
    margin: 1rem 0;
  }

  .slider-hint {
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 0.25rem;
  }
}

// Responsive adjustments for location & branding tab
@media (max-width: 768px) {
  .geofence-section {
    margin-top: 1.5rem;
    padding-top: 1rem;
  }
}