// Main container with two-section layout
.auth-container {
  min-height: 100vh;
  display: flex;
  background: #f8fafc;
}

// Left section - Form
.auth-form-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: white;

  .form-container {
    width: 100%;
    max-width: 500px; // Slightly wider for signup form
  }

  .form-header {
    text-align: center;
    margin-bottom: 2rem;

    h1 {
      margin: 0 0 0.5rem 0;
      font-size: 2rem;
      font-weight: 700;
      color: #1a202c;
      letter-spacing: -0.5px;
    }

    .subtitle {
      margin: 0;
      color: #64748b;
      font-size: 1rem;
    }
  }
}

// Right section - Content
.auth-content-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  .content-container {
    max-width: 500px;
    text-align: center;
  }

  .brand-section {
    margin-bottom: 3rem;

    .logo {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 1rem;
      margin-bottom: 1rem;

      .logo-icon {
        color: rgba(255, 255, 255, 0.9);
      }

      h1 {
        margin: 0;
        font-size: 1.75rem;
        font-weight: 700;
        letter-spacing: -0.5px;
      }
    }

    .tagline {
      margin: 0;
      font-size: 1.125rem;
      opacity: 0.9;
      font-weight: 300;
    }
  }

  .features-section {
    h2 {
      margin: 0 0 2rem 0;
      font-size: 1.5rem;
      font-weight: 600;
      opacity: 0.95;
    }

    .features-list {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
      text-align: left;
    }

    .feature-item {
      display: flex;
      align-items: flex-start;
      gap: 1rem;

      .feature-icon {
        flex-shrink: 0;
        width: 48px;
        height: 48px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;

        svg {
          color: rgba(255, 255, 255, 0.9);
        }
      }

      .feature-content {
        h3 {
          margin: 0 0 0.25rem 0;
          font-size: 1.125rem;
          font-weight: 600;
        }

        p {
          margin: 0;
          font-size: 0.9rem;
          opacity: 0.8;
          line-height: 1.5;
        }
      }
    }
  }
}

// Form styles
.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #fef2f2;
  color: #dc2626;
  padding: 0.875rem 1rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
  border: 1px solid #fecaca;

  svg {
    flex-shrink: 0;
  }
}

.signup-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;

  @media (max-width: 640px) {
    grid-template-columns: 1fr;
  }
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  label {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
  }

  input,
  select {
    padding: 0.875rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: #fafafa;

    &:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      background: white;
    }

    &.error {
      border-color: #dc2626;
      box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
      background: #fef2f2;
    }

    &::placeholder {
      color: #94a3b8;
    }
  }

  select {
    cursor: pointer;

    &:invalid {
      color: #94a3b8;
    }
  }

  .loading-select {
    padding: 0.875rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    color: #94a3b8;
    font-style: italic;
    background: #fafafa;
  }

  .field-error {
    color: #dc2626;
    font-size: 0.75rem;
    font-weight: 500;
  }
}

.signup-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 0.5rem;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

.login-link {
  text-align: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e2e8f0;

  p {
    margin: 0;
    color: #64748b;
    font-size: 0.875rem;

    a {
      color: #667eea;
      text-decoration: none;
      font-weight: 600;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Responsive design
@media (max-width: 1024px) {
  .auth-content-section {
    padding: 2rem;

    .features-section {
      .features-list {
        gap: 1.25rem;
      }
    }
  }
}

@media (max-width: 768px) {
  .auth-container {
    flex-direction: column;
  }

  .auth-content-section {
    order: -1;
    flex: none;
    min-height: 40vh;
    padding: 2rem 1.5rem;

    .brand-section {
      margin-bottom: 2rem;

      .logo {
        flex-direction: column;
        gap: 0.5rem;

        h1 {
          font-size: 1.5rem;
        }
      }

      .tagline {
        font-size: 1rem;
      }
    }

    .features-section {
      display: none; // Hide features on mobile to save space
    }
  }

  .auth-form-section {
    padding: 1.5rem;

    .form-header {
      h1 {
        font-size: 1.75rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .auth-form-section {
    padding: 1rem;
  }

  .auth-content-section {
    padding: 1.5rem 1rem;
    min-height: 30vh;
  }
}