<div class="bulk-import-container">
  <!-- Header -->
  <div class="page-header">
    <div class="header-content">
      <h1>{{ getStageTitle() }}</h1>
      <p>{{ getStageDescription() }}</p>
    </div>
    <div class="header-actions">
      <button mat-raised-button color="primary" (click)="onDownloadTemplate()">
        <mat-icon>download</mat-icon>
        Download Template
      </button>
      <button mat-raised-button (click)="onBackToMembers()">
        <mat-icon>arrow_back</mat-icon>
        Back to Members
      </button>
    </div>
  </div>

  <!-- Initial Stage: File Upload -->
  @if (currentStage() === 'initial') {
  <mat-card class="upload-card">
    <mat-card-content>
      <div 
        class="drop-zone"
        [class.drag-over]="isDragOver()"
        (dragover)="onDragOver($event)"
        (dragleave)="onDragLeave($event)"
        (drop)="onDrop($event)"
        (click)="onSelectFile()">
        
        <div class="drop-zone-content">
          <mat-icon class="upload-icon">cloud_upload</mat-icon>
          <h3>Drag and drop your CSV file here</h3>
          <p>or click to select a file</p>
          <div class="file-requirements">
            <p><strong>Requirements:</strong></p>
            <ul>
              <li>CSV format only</li>
              <li>Maximum file size: 5MB</li>
              <li>Required columns: First Name, Last Name, Email</li>
              <li>Optional columns: Phone Number, Role, Status</li>
            </ul>
          </div>
        </div>
      </div>
      
      <input 
        #fileInput
        type="file" 
        accept=".csv" 
        (change)="onFileSelected($event)"
        style="display: none;">
    </mat-card-content>
  </mat-card>
  }

  <!-- Parsing Stage -->
  @if (currentStage() === 'parsing') {
  <mat-card class="progress-card">
    <mat-card-content>
      <div class="progress-content">
        <mat-icon class="spinning">refresh</mat-icon>
        <h3>Parsing CSV file...</h3>
        <p>Please wait while we process your file.</p>
      </div>
    </mat-card-content>
  </mat-card>
  }

  <!-- Preview Stage -->
  @if (currentStage() === 'preview' && parseResult()) {
  <div class="preview-section">
    <!-- Summary Cards -->
    <div class="summary-cards">
      <mat-card class="summary-card total">
        <mat-card-content>
          <div class="summary-content">
            <mat-icon>people</mat-icon>
            <div class="summary-text">
              <h3>{{ parseResult()!.totalRows }}</h3>
              <p>Total Rows</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="summary-card valid">
        <mat-card-content>
          <div class="summary-content">
            <mat-icon>check_circle</mat-icon>
            <div class="summary-text">
              <h3>{{ parseResult()!.validRows }}</h3>
              <p>Valid Rows</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="summary-card invalid">
        <mat-card-content>
          <div class="summary-content">
            <mat-icon>error</mat-icon>
            <div class="summary-text">
              <h3>{{ parseResult()!.invalidRows }}</h3>
              <p>Invalid Rows</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Data Preview Table -->
    <mat-card class="preview-table-card">
      <mat-card-header>
        <mat-card-title>Data Preview</mat-card-title>
        <mat-card-subtitle>Review the parsed data below. Invalid rows are highlighted in red.</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="table-container">
          <table mat-table [dataSource]="parseResult()!.members" class="preview-table">
            
            <!-- Row Number Column -->
            <ng-container matColumnDef="rowNumber">
              <th mat-header-cell *matHeaderCellDef>Row</th>
              <td mat-cell *matCellDef="let member" [class.invalid-row]="!member.isValid">
                {{ member.rowNumber }}
              </td>
            </ng-container>

            <!-- First Name Column -->
            <ng-container matColumnDef="firstName">
              <th mat-header-cell *matHeaderCellDef>First Name</th>
              <td mat-cell *matCellDef="let member" [class.invalid-row]="!member.isValid">
                {{ member.firstName }}
              </td>
            </ng-container>

            <!-- Last Name Column -->
            <ng-container matColumnDef="lastName">
              <th mat-header-cell *matHeaderCellDef>Last Name</th>
              <td mat-cell *matCellDef="let member" [class.invalid-row]="!member.isValid">
                {{ member.lastName }}
              </td>
            </ng-container>

            <!-- Email Column -->
            <ng-container matColumnDef="email">
              <th mat-header-cell *matHeaderCellDef>Email</th>
              <td mat-cell *matCellDef="let member" [class.invalid-row]="!member.isValid">
                {{ member.email }}
              </td>
            </ng-container>

            <!-- Phone Number Column -->
            <ng-container matColumnDef="phoneNumber">
              <th mat-header-cell *matHeaderCellDef>Phone</th>
              <td mat-cell *matCellDef="let member" [class.invalid-row]="!member.isValid">
                {{ member.phoneNumber }}
              </td>
            </ng-container>

            <!-- Role Column -->
            <ng-container matColumnDef="role">
              <th mat-header-cell *matHeaderCellDef>Role</th>
              <td mat-cell *matCellDef="let member" [class.invalid-row]="!member.isValid">
                {{ member.role }}
              </td>
            </ng-container>

            <!-- Status Column -->
            <ng-container matColumnDef="status">
              <th mat-header-cell *matHeaderCellDef>Status</th>
              <td mat-cell *matCellDef="let member" [class.invalid-row]="!member.isValid">
                {{ member.status }}
              </td>
            </ng-container>

            <!-- Errors Column -->
            <ng-container matColumnDef="errors">
              <th mat-header-cell *matHeaderCellDef>Errors</th>
              <td mat-cell *matCellDef="let member" [class.invalid-row]="!member.isValid">
                @if (member.errors.length > 0) {
                  <mat-chip-set>
                    @for (error of member.errors; track error) {
                      <mat-chip [color]="getErrorChipColor(member)">{{ error }}</mat-chip>
                    }
                  </mat-chip-set>
                } @else {
                  <mat-chip color="primary">Valid</mat-chip>
                }
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
          </table>
        </div>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button (click)="onReset()">
          <mat-icon>refresh</mat-icon>
          Start Over
        </button>
        <button 
          mat-raised-button 
          color="primary" 
          (click)="onStartImport()"
          [disabled]="parseResult()!.validRows === 0">
          <mat-icon>upload</mat-icon>
          Import {{ parseResult()!.validRows }} Valid Members
        </button>
      </mat-card-actions>
    </mat-card>
  </div>
  }

  <!-- Importing Stage -->
  @if (currentStage() === 'importing' && importProgress()) {
  <mat-card class="progress-card">
    <mat-card-content>
      <div class="progress-content">
        <mat-icon class="spinning">sync</mat-icon>
        <h3>Importing Members...</h3>
        <p>{{ getProgressText() }}</p>
        @if (importProgress()!.currentItem) {
          <p class="current-item">Processing: {{ importProgress()!.currentItem }}</p>
        }
        <mat-progress-bar 
          mode="determinate" 
          [value]="getProgressPercentage()">
        </mat-progress-bar>
        <div class="progress-stats">
          <span class="success-count">✅ {{ importProgress()!.successCount }} successful</span>
          <span class="failure-count">❌ {{ importProgress()!.failureCount }} failed</span>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
  }

  <!-- Complete Stage -->
  @if (currentStage() === 'complete' && importResult()) {
  <mat-card class="results-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon [color]="importResult()!.success ? 'primary' : 'warn'">
          {{ importResult()!.success ? 'check_circle' : 'warning' }}
        </mat-icon>
        Import {{ importResult()!.success ? 'Completed' : 'Completed with Errors' }}
      </mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="results-summary">
        <div class="result-stat">
          <h3>{{ importResult()!.totalProcessed }}</h3>
          <p>Total Processed</p>
        </div>
        <div class="result-stat success">
          <h3>{{ importResult()!.successCount }}</h3>
          <p>Successfully Added</p>
        </div>
        <div class="result-stat failure">
          <h3>{{ importResult()!.failureCount }}</h3>
          <p>Failed</p>
        </div>
      </div>

      @if (importResult()!.errors.length > 0) {
        <div class="error-list">
          <h4>Errors:</h4>
          <ul>
            @for (error of importResult()!.errors; track error) {
              <li>{{ error }}</li>
            }
          </ul>
        </div>
      }

      @if (importResult()!.duplicates.length > 0) {
        <div class="duplicate-list">
          <h4>Duplicate Emails Skipped:</h4>
          <ul>
            @for (duplicate of importResult()!.duplicates; track duplicate) {
              <li>{{ duplicate }}</li>
            }
          </ul>
        </div>
      }
    </mat-card-content>
    <mat-card-actions>
      <button mat-raised-button (click)="onReset()">
        <mat-icon>add</mat-icon>
        Import More Members
      </button>
      <button mat-raised-button color="primary" (click)="onBackToMembers()">
        <mat-icon>people</mat-icon>
        View All Members
      </button>
    </mat-card-actions>
  </mat-card>
  }
</div>
