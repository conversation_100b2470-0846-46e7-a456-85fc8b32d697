<div class="dashboard-container">
  <!-- Header -->
  <div class="dashboard-header">
    <div class="header-content">
      <div class="greeting">
        <h1 class="welcome-title">{{ getGreeting() }}, {{ currentUser()?.firstName }}!</h1>
        @if (currentChurch()) {
        <p class="church-name">
          <mat-icon class="church-icon">church</mat-icon>
          {{ currentChurch()!.name }}
        </p>
        }
      </div>
      <div class="header-actions">
        <button mat-fab color="primary" class="refresh-fab" (click)="onRefreshData()" [disabled]="isLoading()">
          <mat-icon>refresh</mat-icon>
        </button>
      </div>
    </div>

    @if (error()) {
    <div class="error-message">
      <mat-icon>error_outline</mat-icon>
      <span>{{ error() }}</span>
      <button mat-raised-button color="primary" (click)="onRetryLoadData()">Retry</button>
    </div>
    }
  </div>

  <!-- Main Content -->
  <div class="dashboard-content">
    @if (isLoading()) {
    <div class="loading-state">
      <mat-spinner diameter="50" color="primary"></mat-spinner>
      <p>Loading your dashboard...</p>
    </div>
    } @else {

    <!-- Stats Overview -->
    <div class="stats-section">
      <h2 class="section-title">
        <mat-icon>analytics</mat-icon>
        Overview
      </h2>
      <div class="stats-grid">
        <mat-card class="stat-card members-card" elevation="2">
          <mat-card-content>
            <div class="stat-layout">
              <div class="stat-icon-container members">
                <mat-icon class="stat-icon">people</mat-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatNumber(dashboardStats().totalMembers) }}</div>
                <h3 class="stat-label">Total Members</h3>
                <div class="stat-trend positive">
                  <mat-icon>trending_up</mat-icon>
                  <span>+12% this month</span>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="stat-card attendance-card" elevation="2">
          <mat-card-content>
            <div class="stat-layout">
              <div class="stat-icon-container attendance">
                <mat-icon class="stat-icon">login</mat-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatNumber(dashboardStats().todayAttendance) }}</div>
                <h3 class="stat-label">Today's Attendance</h3>
                <div class="stat-trend positive">
                  <mat-icon>trending_up</mat-icon>
                  <span>+5% vs last week</span>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="stat-card rate-card" elevation="2">
          <mat-card-content>
            <div class="stat-layout">
              <div class="stat-icon-container rate">
                <mat-icon class="stat-icon">percent</mat-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatPercentage(dashboardStats().attendanceRate) }}</div>
                <h3 class="stat-label">Attendance Rate</h3>
                <div class="stat-trend neutral">
                  <mat-icon>trending_flat</mat-icon>
                  <span>Stable</span>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="stat-card weekly-card" elevation="2">
          <mat-card-content>
            <div class="stat-layout">
              <div class="stat-icon-container weekly">
                <mat-icon class="stat-icon">calendar_month</mat-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ formatNumber(dashboardStats().weeklyAttendance) }}</div>
                <h3 class="stat-label">Weekly Total</h3>
                <div class="stat-trend positive">
                  <mat-icon>trending_up</mat-icon>
                  <span>+8% vs last week</span>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="actions-section">
      <h2 class="section-title">
        <mat-icon>flash_on</mat-icon>
        Quick Actions
      </h2>
      <div class="quick-actions-grid">
        @for (action of quickActions(); track action.route) {
        <mat-card class="action-card" (click)="onQuickAction(action.route)" elevation="1">
          <mat-card-content>
            <div class="action-layout">
              <div class="action-icon-container">
                <mat-icon class="action-icon">{{ action.icon }}</mat-icon>
              </div>
              <div class="action-content">
                <h4 class="action-title">{{ action.title }}</h4>
                <p class="action-description">{{ getActionDescription(action.title) }}</p>
              </div>
              <mat-icon class="action-arrow">arrow_forward</mat-icon>
            </div>
          </mat-card-content>
        </mat-card>
        }
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="activity-section">
      <h2 class="section-title">
        <mat-icon>history</mat-icon>
        Recent Activity
      </h2>
      <mat-card class="activity-card" elevation="1">
        <mat-card-content>
          @if (dashboardStats().recentActivity.length === 0) {
          <div class="empty-state">
            <div class="empty-icon-container">
              <mat-icon class="empty-icon">timeline</mat-icon>
            </div>
            <h3>No Recent Activity</h3>
            <p>Activity will appear here as members check in and interact with the system.</p>
          </div>
          } @else {
          <div class="activity-list">
            @for (activity of dashboardStats().recentActivity; track $index) {
            <div class="activity-item">
              <div class="activity-icon-container">
                <mat-icon class="activity-icon">{{ getActivityIcon(activity.type) }}</mat-icon>
              </div>
              <div class="activity-content">
                <div class="activity-text">
                  <span>{{ activity.message }}</span>
                </div>
                <div class="activity-time">{{ formatTime(activity.timestamp) }}</div>
              </div>
            </div>
            }
          </div>
          }
        </mat-card-content>
      </mat-card>
    </div>
    }
  </div>
</div>