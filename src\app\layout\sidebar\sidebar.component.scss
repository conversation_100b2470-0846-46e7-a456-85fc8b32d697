.sidebar {
  width: 280px;
  height: 100vh;
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 999;
  transition: all 0.3s ease;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);

  &.collapsed {
    width: 80px;

    .sidebar-header .logo h1,
    .sidebar-header .logo .tagline,
    .nav-label {
      display: none;
    }

    .logo-icon {
      display: flex;
    }

    .nav-link {
      justify-content: center;
    }

    .logout-btn {
      justify-content: center;
    }
  }

  @media (max-width: 768px) {
    transform: translateX(-100%);

    &.mobile-open {
      transform: translateX(0);
    }
  }
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .logo {
    display: flex;
    flex-direction: column;
    align-items: center;

    h1 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 700;
      letter-spacing: -0.5px;
    }

    .tagline {
      font-size: 0.8rem;
      opacity: 0.8;
      margin-top: 0.25rem;
    }

    .logo-icon {
      display: none;
      width: 40px;
      height: 40px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      align-items: center;
      justify-content: center;
      font-size: 1.25rem;
      font-weight: 700;
    }
  }
}

.sidebar-nav {
  flex: 1;
  padding: 1rem 0;

  // Override Material Design list styles
  ::ng-deep {
    .mat-mdc-list-base {
      padding: 0;
    }

    .mat-mdc-list-item {
      margin: 0.25rem 0;
      border-radius: 0;

      .mdc-list-item__content {
        padding: 0.875rem 1.5rem;
        color: rgba(255, 255, 255, 0.8) !important;
        transition: all 0.2s ease;
      }

      .mat-mdc-list-item-title {
        color: rgba(255, 255, 255, 0.8) !important;
        font-size: 0.9rem;
        font-weight: 500;
      }

      .mat-icon {
        color: rgba(255, 255, 255, 0.8) !important;
        margin-right: 1rem;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.1) !important;

        .mdc-list-item__content,
        .mat-mdc-list-item-title,
        .mat-icon {
          color: white !important;
        }
      }

      &.active {
        background: rgba(255, 255, 255, 0.15) !important;
        position: relative;

        .mdc-list-item__content,
        .mat-mdc-list-item-title,
        .mat-icon {
          color: white !important;
        }

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 4px;
          background: white;
        }
      }
    }
  }
}

.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);

  // Override Material Design list item styles for logout
  ::ng-deep {
    .mat-mdc-list-item {
      border-radius: 8px;
      cursor: pointer;

      .mdc-list-item__content {
        padding: 0.875rem 0.5rem;
        color: rgba(255, 255, 255, 0.8) !important;
        transition: all 0.2s ease;
      }

      .mat-mdc-list-item-title {
        color: rgba(255, 255, 255, 0.8) !important;
        font-size: 0.9rem;
        font-weight: 500;
      }

      .mat-icon {
        color: rgba(255, 255, 255, 0.8) !important;
        margin-right: 1rem;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.1) !important;

        .mdc-list-item__content,
        .mat-mdc-list-item-title,
        .mat-icon {
          color: white !important;
        }
      }
    }
  }
}