import { Timestamp } from '@angular/fire/firestore';

export interface Attendance {
  id: string;
  memberId: string;
  churchId: string;
  checkInTime: Timestamp;
  checkOutTime?: Timestamp;
  status: 'present' | 'absent' | 'late';
  serviceType: string;
  latitude: number;
  longitude: number;
  locationAccuracy: number;
  notes?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface CreateAttendanceRequest {
  memberId: string;
  churchId: string;
  checkInTime: Timestamp;
  status: 'present' | 'absent' | 'late';
  serviceType: string;
  latitude: number;
  longitude: number;
  locationAccuracy: number;
  notes?: string;
}

export interface UpdateAttendanceRequest {
  checkOutTime?: Timestamp;
  status?: 'present' | 'absent' | 'late';
  notes?: string;
}

export interface AttendanceStats {
  totalMembers: number;
  presentMembers: number;
  absentMembers: number;
  lateMembers: number;
  attendanceRate: number;
}
