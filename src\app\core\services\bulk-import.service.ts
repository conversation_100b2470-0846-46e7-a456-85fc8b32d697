import { Injectable, signal } from '@angular/core';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { map, switchMap, catchError } from 'rxjs/operators';
import { Firestore, collection, addDoc, query, where, getDocs, Timestamp } from '@angular/fire/firestore';
import * as Papa from 'papaparse';
import { AuthService } from './auth.service';
import { MembersService } from './members.service';

// Interfaces for bulk import
export interface ParsedMember {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  role?: string;
  status?: string;
  rowNumber: number;
  errors: string[];
  isValid: boolean;
}

export interface ParseResult {
  members: ParsedMember[];
  totalRows: number;
  validRows: number;
  invalidRows: number;
  errors: string[];
}

export interface ImportProgress {
  stage: 'parsing' | 'validating' | 'importing' | 'complete';
  processed: number;
  total: number;
  percentage: number;
  currentItem?: string;
  errors: string[];
  successCount: number;
  failureCount: number;
}

export interface ImportResult {
  success: boolean;
  totalProcessed: number;
  successCount: number;
  failureCount: number;
  errors: string[];
  duplicates: string[];
}

export interface ColumnMapping {
  [key: string]: string; // CSV column -> member field
}

@Injectable({
  providedIn: 'root'
})
export class BulkImportService {
  // Signals for reactive UI
  isProcessing = signal<boolean>(false);
  progress = signal<ImportProgress | null>(null);
  error = signal<string | null>(null);

  // Progress subject for observables
  private progressSubject = new BehaviorSubject<ImportProgress | null>(null);
  public progress$ = this.progressSubject.asObservable();

  // Column mapping patterns for auto-detection
  private columnMappings: { [key: string]: string[] } = {
    firstName: ['first name', 'firstname', 'first_name', 'fname', 'given name'],
    lastName: ['last name', 'lastname', 'last_name', 'lname', 'surname', 'family name'],
    email: ['email', 'email address', 'e-mail', 'mail', 'email_address'],
    phoneNumber: ['phone', 'phone number', 'mobile', 'cell', 'telephone', 'phone_number', 'mobile_number'],
    role: ['role', 'position', 'title'],
    status: ['status', 'state', 'active']
  };

  constructor(
    private firestore: Firestore,
    private authService: AuthService,
    private membersService: MembersService
  ) {}

  /**
   * Parse CSV file and extract member data
   */
  parseCSV(file: File): Observable<ParseResult> {
    this.isProcessing.set(true);
    this.error.set(null);

    return new Observable(observer => {
      Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        transformHeader: (header: string) => header.trim().toLowerCase(),
        complete: (results) => {
          try {
            const parseResult = this.processParseResults(results);
            this.isProcessing.set(false);
            observer.next(parseResult);
            observer.complete();
          } catch (error) {
            this.isProcessing.set(false);
            this.error.set('Failed to parse CSV file');
            observer.error(error);
          }
        },
        error: (error) => {
          this.isProcessing.set(false);
          this.error.set('Failed to read CSV file');
          observer.error(error);
        }
      });
    });
  }

  /**
   * Process Papa Parse results into our format
   */
  private processParseResults(results: Papa.ParseResult<any>): ParseResult {
    const members: ParsedMember[] = [];
    const globalErrors: string[] = [];

    // Auto-detect column mapping
    const columnMapping = this.detectColumnMapping(results.meta.fields || []);

    results.data.forEach((row: any, index: number) => {
      const rowNumber = index + 2; // +2 because index is 0-based and we skip header
      const member = this.mapRowToMember(row, columnMapping, rowNumber);
      members.push(member);
    });

    // Add Papa Parse errors to global errors
    if (results.errors && results.errors.length > 0) {
      results.errors.forEach(error => {
        globalErrors.push(`Row ${error.row}: ${error.message}`);
      });
    }

    const validRows = members.filter(m => m.isValid).length;
    const invalidRows = members.length - validRows;

    return {
      members,
      totalRows: members.length,
      validRows,
      invalidRows,
      errors: globalErrors
    };
  }

  /**
   * Auto-detect column mapping based on header names
   */
  private detectColumnMapping(headers: string[]): ColumnMapping {
    const mapping: ColumnMapping = {};

    headers.forEach(header => {
      const normalizedHeader = header.toLowerCase().trim();
      
      // Find matching field
      for (const [field, patterns] of Object.entries(this.columnMappings)) {
        if (patterns.includes(normalizedHeader)) {
          mapping[header] = field;
          break;
        }
      }
    });

    return mapping;
  }

  /**
   * Map CSV row to ParsedMember object
   */
  private mapRowToMember(row: any, columnMapping: ColumnMapping, rowNumber: number): ParsedMember {
    const member: ParsedMember = {
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      role: 'member',
      status: 'active',
      rowNumber,
      errors: [],
      isValid: true
    };

    // Map columns to member fields
    Object.entries(row).forEach(([csvColumn, value]) => {
      const memberField = columnMapping[csvColumn];
      if (memberField && value) {
        (member as any)[memberField] = String(value).trim();
      }
    });

    // Validate required fields
    if (!member.firstName) {
      member.errors.push('First name is required');
      member.isValid = false;
    }

    if (!member.lastName) {
      member.errors.push('Last name is required');
      member.isValid = false;
    }

    if (!member.email) {
      member.errors.push('Email is required');
      member.isValid = false;
    } else if (!this.isValidEmail(member.email)) {
      member.errors.push('Invalid email format');
      member.isValid = false;
    }

    // Validate phone number format (Ghana format)
    if (member.phoneNumber && !this.isValidPhoneNumber(member.phoneNumber)) {
      member.errors.push('Invalid phone number format (use +233... format)');
      member.isValid = false;
    }

    // Validate role
    if (member.role && !['member', 'admin', 'pastor'].includes(member.role.toLowerCase())) {
      member.errors.push('Invalid role (must be: member, admin, or pastor)');
      member.isValid = false;
    }

    return member;
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate Ghana phone number format
   */
  private isValidPhoneNumber(phone: string): boolean {
    // Ghana phone number patterns: +233... or 0...
    const ghanaPhoneRegex = /^(\+233|0)[0-9]{9}$/;
    return ghanaPhoneRegex.test(phone.replace(/\s/g, ''));
  }

  /**
   * Check for duplicate emails in the current church
   */
  checkDuplicates(members: ParsedMember[], churchId: string): Observable<string[]> {
    const emails = members.filter(m => m.isValid).map(m => m.email.toLowerCase());
    
    if (emails.length === 0) {
      return of([]);
    }

    return this.membersService.getChurchMembers(churchId).pipe(
      map(existingMembers => {
        const existingEmails = existingMembers.map(m => m.email.toLowerCase());
        return emails.filter(email => existingEmails.includes(email));
      }),
      catchError(error => {
        console.error('Error checking duplicates:', error);
        return of([]);
      })
    );
  }

  /**
   * Import valid members to Firestore
   */
  importMembers(members: ParsedMember[], churchId: string): Observable<ImportResult> {
    const validMembers = members.filter(m => m.isValid);
    
    if (validMembers.length === 0) {
      return of({
        success: false,
        totalProcessed: 0,
        successCount: 0,
        failureCount: 0,
        errors: ['No valid members to import'],
        duplicates: []
      });
    }

    this.isProcessing.set(true);
    
    return new Observable(observer => {
      this.performBulkImport(validMembers, churchId).then(result => {
        this.isProcessing.set(false);
        observer.next(result);
        observer.complete();
      }).catch(error => {
        this.isProcessing.set(false);
        observer.error(error);
      });
    });
  }

  /**
   * Perform the actual bulk import with progress tracking
   */
  private async performBulkImport(members: ParsedMember[], churchId: string): Promise<ImportResult> {
    const result: ImportResult = {
      success: true,
      totalProcessed: members.length,
      successCount: 0,
      failureCount: 0,
      errors: [],
      duplicates: []
    };

    const batchSize = 10; // Process in batches to avoid overwhelming Firestore
    const batches = this.chunkArray(members, batchSize);

    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      
      for (let memberIndex = 0; memberIndex < batch.length; memberIndex++) {
        const member = batch[memberIndex];
        const overallIndex = batchIndex * batchSize + memberIndex;
        
        // Update progress
        const progress: ImportProgress = {
          stage: 'importing',
          processed: overallIndex + 1,
          total: members.length,
          percentage: Math.round(((overallIndex + 1) / members.length) * 100),
          currentItem: `${member.firstName} ${member.lastName}`,
          errors: result.errors,
          successCount: result.successCount,
          failureCount: result.failureCount
        };
        
        this.progress.set(progress);
        this.progressSubject.next(progress);

        try {
          await this.createMember(member, churchId);
          result.successCount++;
        } catch (error: any) {
          result.failureCount++;
          result.errors.push(`Row ${member.rowNumber}: ${error.message}`);
        }

        // Small delay to prevent overwhelming Firestore
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    // Final progress update
    const finalProgress: ImportProgress = {
      stage: 'complete',
      processed: members.length,
      total: members.length,
      percentage: 100,
      errors: result.errors,
      successCount: result.successCount,
      failureCount: result.failureCount
    };
    
    this.progress.set(finalProgress);
    this.progressSubject.next(finalProgress);

    result.success = result.failureCount === 0;
    return result;
  }

  /**
   * Create a single member in Firestore
   */
  private async createMember(member: ParsedMember, churchId: string): Promise<void> {
    const usersCollection = collection(this.firestore, 'users');
    
    const memberData = {
      email: member.email,
      firstName: member.firstName,
      lastName: member.lastName,
      fullName: `${member.firstName} ${member.lastName}`,
      churchId: churchId,
      role: member.role || 'member',
      status: member.status || 'active',
      phoneNumber: member.phoneNumber || '',
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      importedAt: Timestamp.now() // Track that this was bulk imported
    };

    await addDoc(usersCollection, memberData);
  }

  /**
   * Utility function to chunk array into smaller arrays
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Get suggested column mappings for manual mapping
   */
  getSuggestedMappings(headers: string[]): { [key: string]: string[] } {
    const suggestions: { [key: string]: string[] } = {};
    
    Object.keys(this.columnMappings).forEach(field => {
      suggestions[field] = this.columnMappings[field];
    });
    
    return suggestions;
  }

  /**
   * Clear current progress and errors
   */
  clearState(): void {
    this.progress.set(null);
    this.error.set(null);
    this.isProcessing.set(false);
    this.progressSubject.next(null);
  }
}
