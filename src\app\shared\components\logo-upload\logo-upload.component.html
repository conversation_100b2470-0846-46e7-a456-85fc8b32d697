<div class="logo-upload">
  <mat-card class="logo-card">
    <mat-card-header>
      <mat-card-title>Church Logo</mat-card-title>
      <mat-card-subtitle>Upload your church logo (JPG, PNG, GIF - Max 2MB)</mat-card-subtitle>
    </mat-card-header>
    
    <mat-card-content>
      <!-- Current Logo Display -->
      @if (currentLogoUrl) {
        <div class="current-logo">
          <img [src]="currentLogoUrl" alt="Current church logo" class="logo-preview">
          <div class="logo-actions">
            <button mat-raised-button color="primary" (click)="triggerFileInput()" [disabled]="isUploading()">
              <mat-icon>edit</mat-icon>
              Replace Logo
            </button>
            <button mat-button color="warn" (click)="onDeleteLogo()" [disabled]="isUploading()">
              <mat-icon>delete</mat-icon>
              Remove Logo
            </button>
          </div>
        </div>
      } @else {
        <!-- No Logo State -->
        <div class="no-logo">
          <div class="upload-placeholder" (click)="triggerFileInput()" [class.disabled]="isUploading()">
            @if (isUploading()) {
              <mat-spinner diameter="40"></mat-spinner>
              <p>Uploading...</p>
            } @else {
              <mat-icon class="upload-icon">cloud_upload</mat-icon>
              <p>Click to upload logo</p>
              <small>JPG, PNG, GIF - Max 2MB</small>
            }
          </div>
        </div>
      }

      <!-- File Input (Hidden) -->
      <input #fileInput
             type="file"
             accept="image/jpeg,image/jpg,image/png,image/gif"
             (change)="onFileSelected($event)"
             style="display: none;">

      <!-- Upload Progress -->
      @if (isUploading()) {
        <div class="upload-progress">
          <mat-spinner diameter="24"></mat-spinner>
          <span>Uploading logo...</span>
        </div>
      }
    </mat-card-content>
  </mat-card>
</div>
