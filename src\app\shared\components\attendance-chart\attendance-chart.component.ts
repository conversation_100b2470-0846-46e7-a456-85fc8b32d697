import { Component, Input, OnInit, OnChanges, SimpleChanges, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Chart, ChartConfiguration, ChartType, registerables } from 'chart.js';

Chart.register(...registerables);

export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
    fill?: boolean;
  }[];
}

export interface ChartOptions {
  responsive?: boolean;
  maintainAspectRatio?: boolean;
  plugins?: {
    legend?: {
      display?: boolean;
      position?: 'top' | 'bottom' | 'left' | 'right';
    };
    title?: {
      display?: boolean;
      text?: string;
    };
  };
  scales?: {
    x?: {
      display?: boolean;
      title?: {
        display?: boolean;
        text?: string;
      };
    };
    y?: {
      display?: boolean;
      beginAtZero?: boolean;
      title?: {
        display?: boolean;
        text?: string;
      };
    };
  };
}

@Component({
  selector: 'app-attendance-chart',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="chart-container">
      @if (title) {
        <h3 class="chart-title">{{ title }}</h3>
      }
      <div class="chart-wrapper">
        <canvas #chartCanvas></canvas>
      </div>
      @if (loading) {
        <div class="chart-loading">
          <div class="loading-spinner"></div>
          <p>Loading chart data...</p>
        </div>
      }
      @if (error) {
        <div class="chart-error">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z"/>
          </svg>
          <p>{{ error }}</p>
        </div>
      }
    </div>
  `,
  styles: [`
    .chart-container {
      position: relative;
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      margin-bottom: 1.5rem;
    }

    .chart-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #2d3748;
      margin: 0 0 1rem 0;
      text-align: center;
    }

    .chart-wrapper {
      position: relative;
      height: 400px;
      width: 100%;
    }

    .chart-loading {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 12px;

      .loading-spinner {
        width: 32px;
        height: 32px;
        border: 3px solid #e2e8f0;
        border-top: 3px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
      }

      p {
        color: #718096;
        font-size: 0.875rem;
        margin: 0;
      }
    }

    .chart-error {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: #e53e3e;
      text-align: center;

      svg {
        margin-bottom: 0.5rem;
      }

      p {
        margin: 0;
        font-size: 0.875rem;
      }
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @media (max-width: 768px) {
      .chart-container {
        padding: 1rem;
      }

      .chart-wrapper {
        height: 300px;
      }
    }
  `]
})
export class AttendanceChartComponent implements OnInit, OnChanges {
  @Input() chartType: ChartType = 'line';
  @Input() data: ChartData | null = null;
  @Input() options: ChartOptions = {};
  @Input() title: string = '';
  @Input() loading: boolean = false;
  @Input() error: string = '';

  @ViewChild('chartCanvas', { static: true }) chartCanvas!: ElementRef<HTMLCanvasElement>;

  private chart: Chart | null = null;

  ngOnInit(): void {
    this.initializeChart();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['data'] && this.chart && this.data) {
      this.updateChart();
    }
  }

  private initializeChart(): void {
    if (!this.chartCanvas || !this.data) return;

    const ctx = this.chartCanvas.nativeElement.getContext('2d');
    if (!ctx) return;

    const defaultOptions: ChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: true,
          position: 'top',
        },
        title: {
          display: false,
        },
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: 'Time Period',
          },
        },
        y: {
          display: true,
          beginAtZero: true,
          title: {
            display: true,
            text: 'Attendance Count',
          },
        },
      },
    };

    const mergedOptions = this.mergeOptions(defaultOptions, this.options);

    const config: ChartConfiguration = {
      type: this.chartType,
      data: {
        labels: this.data.labels,
        datasets: this.data.datasets.map(dataset => ({
          ...dataset,
          backgroundColor: dataset.backgroundColor || this.getDefaultColors().background,
          borderColor: dataset.borderColor || this.getDefaultColors().border,
          borderWidth: dataset.borderWidth || 2,
          fill: dataset.fill !== undefined ? dataset.fill : false,
        })),
      },
      options: mergedOptions,
    };

    this.chart = new Chart(ctx, config);
  }

  private updateChart(): void {
    if (!this.chart || !this.data) return;

    this.chart.data.labels = this.data.labels;
    this.chart.data.datasets = this.data.datasets.map(dataset => ({
      ...dataset,
      backgroundColor: dataset.backgroundColor || this.getDefaultColors().background,
      borderColor: dataset.borderColor || this.getDefaultColors().border,
      borderWidth: dataset.borderWidth || 2,
      fill: dataset.fill !== undefined ? dataset.fill : false,
    }));

    this.chart.update();
  }

  private mergeOptions(defaultOptions: ChartOptions, customOptions: ChartOptions): ChartOptions {
    return {
      ...defaultOptions,
      ...customOptions,
      plugins: {
        ...defaultOptions.plugins,
        ...customOptions.plugins,
      },
      scales: {
        ...defaultOptions.scales,
        ...customOptions.scales,
      },
    };
  }

  private getDefaultColors() {
    return {
      background: 'rgba(102, 126, 234, 0.1)',
      border: 'rgba(102, 126, 234, 1)',
    };
  }

  ngOnDestroy(): void {
    if (this.chart) {
      this.chart.destroy();
    }
  }
}
