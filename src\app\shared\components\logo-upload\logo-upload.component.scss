.logo-upload {
  width: 100%;
}

.logo-card {
  .current-logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;

    .logo-preview {
      max-width: 200px;
      max-height: 200px;
      object-fit: contain;
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      padding: 0.5rem;
      background: white;
    }

    .logo-actions {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
      justify-content: center;
    }
  }

  .no-logo {
    .upload-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 200px;
      border: 2px dashed #ccc;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      background: #fafafa;

      &:hover:not(.disabled) {
        border-color: #2196f3;
        background: #f0f8ff;
      }

      &.disabled {
        cursor: not-allowed;
        opacity: 0.6;
      }

      .upload-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
        color: #666;
        margin-bottom: 0.5rem;
      }

      p {
        margin: 0.5rem 0;
        font-size: 1.1rem;
        color: #333;
      }

      small {
        color: #666;
        font-size: 0.9rem;
      }
    }
  }

  .upload-progress {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
    padding: 1rem;
    background: #f5f5f5;
    border-radius: 4px;

    span {
      color: #666;
    }
  }
}
