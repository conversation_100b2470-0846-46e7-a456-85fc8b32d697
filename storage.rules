rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    // Profile pictures
    match /profile_pictures/{userId}/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Church content (logos, videos, etc.)
    match /churches/{churchId}/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
                      isChurchAdmin(churchId);
    }
  }
  
  function isChurchAdmin(churchId) {
    return exists(/databases/(default)/documents/users/$(request.auth.uid)) &&
           get(/databases/(default)/documents/users/$(request.auth.uid)).data.churchId == churchId &&
           get(/databases/(default)/documents/users/$(request.auth.uid)).data.role in ['admin', 'pastor'];
  }
}
