import { Injectable, signal } from '@angular/core';
import { Firestore, doc, updateDoc, getDoc, collection, query, where, getDocs, Timestamp } from '@angular/fire/firestore';
import { Observable, from, BehaviorSubject } from 'rxjs';
import { map, tap, catchError } from 'rxjs/operators';

import { User, CreateUserRequest } from '../../shared/models/user.model';
import { Church } from '../../shared/models/church.model';
import { AuthService } from './auth.service';

export interface AdminPrivileges {
  canManageMembers: boolean;
  canManageSettings: boolean;
  canViewReports: boolean;
  canManageAttendance: boolean;
  canManageAdmins: boolean;
  canDeleteChurch: boolean;
}

export interface AdminUser extends User {
  privileges: AdminPrivileges;
  assignedBy: string;
  assignedAt: Timestamp;
}

@Injectable({
  providedIn: 'root'
})
export class AdminService {
  // Signals for reactive UI
  isLoading = signal<boolean>(false);
  error = signal<string | null>(null);

  // BehaviorSubjects for components that need observables
  private adminUsersSubject = new BehaviorSubject<AdminUser[]>([]);

  constructor(
    private firestore: Firestore,
    private authService: AuthService
  ) {}

  /**
   * Create a new admin user for a church
   */
  async createChurchAdmin(
    churchId: string,
    adminData: CreateUserRequest,
    password: string,
    privileges?: Partial<AdminPrivileges>
  ): Promise<void> {
    this.isLoading.set(true);
    this.error.set(null);

    try {
      // Create the user account
      await this.authService.signUp({
        ...adminData,
        churchId,
        role: 'admin'
      }, password);

      // Get the created user
      const currentUser = this.authService.currentUser();
      if (!currentUser) {
        throw new Error('Failed to create admin user');
      }

      // Set admin privileges
      const defaultPrivileges: AdminPrivileges = {
        canManageMembers: true,
        canManageSettings: true,
        canViewReports: true,
        canManageAttendance: true,
        canManageAdmins: false,
        canDeleteChurch: false,
        ...privileges
      };

      await this.setAdminPrivileges(currentUser.id, defaultPrivileges);

      // Add admin to church's admin list
      await this.addAdminToChurch(churchId, currentUser.id);

      this.isLoading.set(false);
    } catch (error: any) {
      this.error.set(error.message || 'Failed to create admin user');
      this.isLoading.set(false);
      throw error;
    }
  }

  /**
   * Set admin privileges for a user
   */
  async setAdminPrivileges(userId: string, privileges: AdminPrivileges): Promise<void> {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      throw new Error('Not authenticated');
    }

    const userRef = doc(this.firestore, 'users', userId);
    const privilegesData = {
      privileges,
      assignedBy: currentUser.id,
      assignedAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    };

    return from(updateDoc(userRef, privilegesData)).toPromise();
  }

  /**
   * Get admin privileges for a user
   */
  getAdminPrivileges(userId: string): Observable<AdminPrivileges | null> {
    const userRef = doc(this.firestore, 'users', userId);

    return from(getDoc(userRef)).pipe(
      map(docSnap => {
        if (docSnap.exists()) {
          const userData = docSnap.data();
          return userData['privileges'] || null;
        }
        return null;
      }),
      catchError(error => {
        console.error('Error getting admin privileges:', error);
        throw error;
      })
    );
  }

  /**
   * Get all admin users for a church
   */
  getChurchAdmins(churchId: string): Observable<AdminUser[]> {
    this.isLoading.set(true);
    this.error.set(null);

    const usersRef = collection(this.firestore, 'users');
    const q = query(
      usersRef,
      where('churchId', '==', churchId),
      where('role', 'in', ['admin', 'pastor'])
    );

    return from(getDocs(q)).pipe(
      map(snapshot => {
        const admins = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        } as AdminUser));
        this.isLoading.set(false);
        this.adminUsersSubject.next(admins);
        return admins;
      }),
      catchError(error => {
        this.error.set('Failed to load admin users');
        this.isLoading.set(false);
        throw error;
      })
    );
  }

  /**
   * Add admin to church's admin list
   */
  private async addAdminToChurch(churchId: string, adminId: string): Promise<void> {
    const churchRef = doc(this.firestore, 'churches', churchId);
    const churchDoc = await getDoc(churchRef);

    if (churchDoc.exists()) {
      const churchData = churchDoc.data() as Church;
      const currentAdminIds = churchData.adminIds || [];

      if (!currentAdminIds.includes(adminId)) {
        await updateDoc(churchRef, {
          adminIds: [...currentAdminIds, adminId],
          updatedAt: Timestamp.now()
        });
      }
    }
  }

  /**
   * Remove admin from church
   */
  async removeAdminFromChurch(churchId: string, adminId: string): Promise<void> {
    this.isLoading.set(true);
    this.error.set(null);

    try {
      // Remove from church's admin list
      const churchRef = doc(this.firestore, 'churches', churchId);
      const churchDoc = await getDoc(churchRef);

      if (churchDoc.exists()) {
        const churchData = churchDoc.data() as Church;
        const updatedAdminIds = (churchData.adminIds || []).filter(id => id !== adminId);

        await updateDoc(churchRef, {
          adminIds: updatedAdminIds,
          updatedAt: Timestamp.now()
        });
      }

      // Update user role to member
      const userRef = doc(this.firestore, 'users', adminId);
      await updateDoc(userRef, {
        role: 'member',
        privileges: null,
        assignedBy: null,
        assignedAt: null,
        updatedAt: Timestamp.now()
      });

      this.isLoading.set(false);
    } catch (error: any) {
      this.error.set('Failed to remove admin');
      this.isLoading.set(false);
      throw error;
    }
  }

  /**
   * Check if current user can perform admin action
   */
  canPerformAction(action: keyof AdminPrivileges): boolean {
    const currentUser = this.authService.currentUser();
    if (!currentUser) return false;

    // Super admin can do everything
    if (currentUser.role === 'super_admin') return true;

    // Check specific privilege
    const privileges = (currentUser as any).privileges as AdminPrivileges;
    return privileges ? privileges[action] : false;
  }

  /**
   * Get default admin privileges based on role
   */
  getDefaultPrivileges(role: 'admin' | 'pastor'): AdminPrivileges {
    const basePrivileges: AdminPrivileges = {
      canManageMembers: true,
      canManageSettings: false,
      canViewReports: true,
      canManageAttendance: true,
      canManageAdmins: false,
      canDeleteChurch: false
    };

    if (role === 'admin') {
      return {
        ...basePrivileges,
        canManageSettings: true,
        canManageAdmins: true
      };
    }

    return basePrivileges;
  }

  // Observable getters
  get adminUsers$(): Observable<AdminUser[]> {
    return this.adminUsersSubject.asObservable();
  }
}
