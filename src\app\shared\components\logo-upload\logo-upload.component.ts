import { Component, Input, Output, EventEmitter, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatCardModule } from '@angular/material/card';

@Component({
  selector: 'app-logo-upload',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatCardModule
  ],
  templateUrl: './logo-upload.component.html',
  styleUrl: './logo-upload.component.scss'
})
export class LogoUploadComponent {
  @Input() currentLogoUrl: string | null = null;
  @Output() logoUploaded = new EventEmitter<File>();
  @Output() logoDeleted = new EventEmitter<void>();

  // Signals
  isUploading = signal<boolean>(false);

  constructor(private snackBar: MatSnackBar) { }

  triggerFileInput(): void {
    if (this.isUploading()) return;

    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    fileInput?.click();
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];

    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
      this.snackBar.open('Please select a valid image file (JPG, PNG, or GIF)', 'Close', { duration: 5000 });
      return;
    }

    // Validate file size (2MB max)
    const maxSize = 2 * 1024 * 1024; // 2MB in bytes
    if (file.size > maxSize) {
      this.snackBar.open('File size must be less than 2MB', 'Close', { duration: 5000 });
      return;
    }

    // Emit the file for parent component to handle upload
    this.logoUploaded.emit(file);

    // Clear the input
    input.value = '';
  }

  onDeleteLogo(): void {
    if (this.isUploading()) return;

    this.logoDeleted.emit();
  }

  setUploadingState(uploading: boolean): void {
    this.isUploading.set(uploading);
  }
}
