import * as functions from "firebase-functions";
import * as admin from "firebase-admin";

admin.initializeApp();

// Example function for church management
export const createChurchAdmin = functions.https.onCall(async (data, context) => {
  // Verify the user is authenticated
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  // Add your church admin creation logic here
  return { success: true, message: 'Church admin created successfully' };
});

// Example function for user management
export const updateUserRole = functions.https.onCall(async (data, context) => {
  // Verify the user is authenticated and has admin privileges
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  // Add your user role update logic here
  return { success: true, message: 'User role updated successfully' };
});

// Cloud Function to send FCM notifications
export const sendNotification = functions.https.onCall(async (data, context) => {
  // Verify the user is authenticated
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { title, body, tokens, data: notificationData } = data;

  if (!title || !body || !tokens || !Array.isArray(tokens)) {
    throw new functions.https.HttpsError('invalid-argument', 'Missing required fields');
  }

  try {
    const message = {
      notification: {
        title,
        body,
      },
      data: notificationData || {},
      tokens,
    };

    const response = await admin.messaging().sendMulticast(message);

    return {
      success: true,
      successCount: response.successCount,
      failureCount: response.failureCount,
      responses: response.responses.map(resp => ({
        success: resp.success,
        error: resp.error?.message || null
      }))
    };
  } catch (error) {
    console.error('Error sending notification:', error);
    throw new functions.https.HttpsError('internal', 'Failed to send notification');
  }
});

// Cloud Function to handle notification document creation
export const onNotificationCreated = functions.firestore
  .document('notifications/{notificationId}')
  .onCreate(async (snap, context) => {
    const notificationData = snap.data();

    if (notificationData.status !== 'pending') {
      return;
    }

    try {
      const message = {
        notification: {
          title: notificationData.title,
          body: notificationData.body,
          icon: notificationData.icon || '/favicon.ico',
        },
        data: notificationData.data || {},
        tokens: notificationData.targetTokens,
      };

      const response = await admin.messaging().sendMulticast(message);

      // Update the notification document with the result
      await snap.ref.update({
        status: 'sent',
        result: {
          successCount: response.successCount,
          failureCount: response.failureCount,
          errors: response.responses
            .filter(resp => !resp.success)
            .map(resp => resp.error?.message || 'Unknown error')
        },
        sentAt: admin.firestore.FieldValue.serverTimestamp()
      });

      console.log(`Notification sent: ${response.successCount} successful, ${response.failureCount} failed`);
    } catch (error) {
      console.error('Error sending notification:', error);

      // Update the notification document with error status
      await snap.ref.update({
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        sentAt: admin.firestore.FieldValue.serverTimestamp()
      });
    }
  });
