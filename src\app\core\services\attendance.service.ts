import { Injectable, signal, inject } from '@angular/core';
import { addDoc, collection, doc, Firestore, getDocs, orderBy, query, Timestamp, updateDoc, where, getDoc } from '@angular/fire/firestore';
import { BehaviorSubject, Observable, of, forkJoin } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';

import { Attendance, CreateAttendanceRequest, UpdateAttendanceRequest } from '@shared/models/attendance.model';
import { AuthService } from './auth.service';
import { User } from '@shared/models/user.model';

export interface AttendanceRecord {
  id: string;
  memberId: string;
  checkInTime: string;
  checkOutTime?: string;
  method: string;
  distance?: number;
  accuracy?: number;
  duration?: number;
  member?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    fullName: string;
    profilePicture?: string;
  };
}

export interface LiveAttendance {
  totalPresent: number;
  totalCheckedIn: number;
  recentCheckIns: AttendanceRecord[];
  attendanceRate: number;
}

export interface AttendanceStats {
  todayAttendance: number;
  weeklyAttendance: number;
  monthlyAttendance: number;
  attendanceRate: number;
  totalMembers: number;
  averageAttendance: number;
  trends: {
    daily: { date: string; count: number }[];
    weekly: { week: string; count: number }[];
  };
}

export interface WeeklyReport {
  period: string;
  startDate: string;
  endDate: string;
  totalWeeks: number;
  weeklyData: WeeklyReportData[];
  summary: {
    totalAttendance: number;
    averageWeeklyAttendance: number;
    peakWeek: WeeklyReportData;
  };
}

export interface WeeklyReportData {
  weekStart: string;
  totalAttendance: number;
  uniqueMembers: number;
  servicesHeld: number;
  flaggedCount: number;
  verifiedCount: number;
  verificationRate: number;
  averagePerService: number;
}

export interface MonthlyReport {
  period: string;
  startDate: string;
  endDate: string;
  totalMonths: number;
  monthlyData: MonthlyReportData[];
  summary: {
    totalAttendance: number;
    averageMonthlyAttendance: number;
    peakMonth: MonthlyReportData;
  };
}

export interface MonthlyReportData {
  month: string;
  totalAttendance: number;
  uniqueMembers: number;
  servicesHeld: number;
  weekendServices: number;
  flaggedCount: number;
  verifiedCount: number;
  verificationRate: number;
  averagePerService: number;
}

export interface AttendanceTrends {
  period: string;
  dateRange: { startDate: string; endDate: string };
  trends: {
    growth: TrendAnalysis;
    engagement: EngagementAnalysis;
    services: ServiceAnalysis;
  };
  insights: string[];
}

export interface TrendAnalysis {
  trend: 'growing' | 'declining' | 'stable' | 'insufficient_data';
  growthRate: number;
  direction: 'up' | 'down' | 'stable';
  recentAverage: number;
  previousAverage: number;
}

export interface EngagementAnalysis {
  totalActiveMembers: number;
  averageAttendancePerMember: number;
  regularAttenders: number;
  occasionalAttenders: number;
  infrequentAttenders: number;
}

export interface ServiceAnalysis {
  byDayOfWeek: { [key: string]: number };
  mostPopularDay: { day: string; count: number };
}

export interface ExportData {
  format: string;
  reportType: string;
  generatedAt: string;
  dateRange: { startDate: string; endDate: string };
  data: any;
}

export interface CheckInRequest {
  latitude: number;
  longitude: number;
  accuracy: number;
  attendanceSessionId?: string;
  method?: string;
  serviceType?: string;
  deviceInfo?: any;
}

export interface CheckOutRequest {
  attendanceRecordId: string;
}

export interface LocationValidation {
  isValid: boolean;
  distance: number;
  message: string;
  churchName?: string;
}

@Injectable({
  providedIn: 'root'
})
export class AttendanceService {
  // Firebase-based service - no API URL needed

  // Signals for reactive UI
  liveAttendance = signal<LiveAttendance | null>(null);
  attendanceStats = signal<AttendanceStats | null>(null);
  isLoading = signal<boolean>(false);

  // BehaviorSubjects for components that need observables
  private liveAttendanceSubject = new BehaviorSubject<LiveAttendance | null>(null);
  private attendanceStatsSubject = new BehaviorSubject<AttendanceStats | null>(null);

  constructor(
    private firestore: Firestore,
    private authService: AuthService
  ) { }

  /**
   * Calculate distance between two coordinates using Haversine formula
   */
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c; // Distance in meters
  }

  /**
   * Get church location coordinates
   */
  private async getChurchLocation(churchId: string): Promise<{ latitude: number; longitude: number } | null> {
    try {
      const churchRef = doc(this.firestore, 'churches', churchId);
      const churchDoc = await getDoc(churchRef);

      if (churchDoc.exists()) {
        const churchData = churchDoc.data();
        if (churchData['latitude'] && churchData['longitude']) {
          return {
            latitude: churchData['latitude'],
            longitude: churchData['longitude']
          };
        }
      }
      return null;
    } catch (error) {
      console.error('Error fetching church location:', error);
      return null;
    }
  }

  /**
   * Get member data by ID
   */
  private async getMemberData(memberId: string): Promise<User | null> {
    try {
      const memberRef = doc(this.firestore, 'users', memberId);
      const memberDoc = await getDoc(memberRef);

      if (memberDoc.exists()) {
        const memberData = memberDoc.data() as User;
        return {
          ...memberData,
          id: memberDoc.id,
          fullName: memberData.fullName || `${memberData.firstName} ${memberData.lastName}`
        };
      }
      return null;
    } catch (error) {
      console.error('Error fetching member data:', error);
      return null;
    }
  }

  /**
   * Check in to church service
   */
  checkIn(checkInData: CheckInRequest): Observable<{ message: string; attendance: AttendanceRecord }> {
    const currentUser = this.authService.currentUser();

    if (!currentUser) {
      return new Observable(observer => {
        observer.error(new Error('User not authenticated'));
      });
    }

    if (!currentUser.churchId) {
      return new Observable(observer => {
        observer.error(new Error('User not associated with a church'));
      });
    }

    const attendanceCollection = collection(this.firestore, 'attendance');

    const attendanceData: CreateAttendanceRequest = {
      memberId: currentUser.id,
      churchId: currentUser.churchId,
      checkInTime: Timestamp.now(),
      status: 'present',
      serviceType: checkInData.serviceType || 'Sunday Service',
      latitude: checkInData.latitude,
      longitude: checkInData.longitude,
      locationAccuracy: checkInData.accuracy
    };

    return new Observable(observer => {
      // Get church location and member data in parallel
      Promise.all([
        this.getChurchLocation(currentUser.churchId!),
        this.getMemberData(currentUser.id)
      ]).then(([churchLocation, memberData]) => {

        // Calculate distance if church location is available
        let distance: number | undefined;
        if (churchLocation) {
          distance = this.calculateDistance(
            checkInData.latitude,
            checkInData.longitude,
            churchLocation.latitude,
            churchLocation.longitude
          );
        }

        // Add attendance record to Firestore
        return addDoc(attendanceCollection, {
          ...attendanceData,
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now()
        }).then(docRef => {
          const attendance: AttendanceRecord = {
            id: docRef.id,
            memberId: attendanceData.memberId,
            checkInTime: attendanceData.checkInTime.toDate().toISOString(),
            method: checkInData.method || 'location',
            accuracy: checkInData.accuracy,
            distance: distance,
            member: memberData ? {
              id: memberData.id,
              firstName: memberData.firstName,
              lastName: memberData.lastName,
              email: memberData.email,
              fullName: memberData.fullName || `${memberData.firstName} ${memberData.lastName}`,
              profilePicture: memberData.profilePicture
            } : undefined
          };
          observer.next({ message: 'Check-in successful', attendance });
          observer.complete();
        });
      }).catch(error => {
        observer.error(error);
      });
    });
  }

  /**
   * Check out from church service
   */
  checkOut(checkOutData: CheckOutRequest): Observable<{ message: string; attendance: AttendanceRecord }> {
    const attendanceRef = doc(this.firestore, 'attendance', checkOutData.attendanceRecordId);

    const updateData: UpdateAttendanceRequest = {
      checkOutTime: Timestamp.now()
    };

    return new Observable(observer => {
      updateDoc(attendanceRef, {
        ...updateData,
        updatedAt: Timestamp.now()
      }).then(() => {
        const attendance: AttendanceRecord = {
          id: checkOutData.attendanceRecordId,
          memberId: 'current-user-id', // TODO: Get from document
          checkInTime: new Date().toISOString(), // TODO: Get from document
          checkOutTime: updateData.checkOutTime!.toDate().toISOString(),
          method: 'location'
        };
        observer.next({ message: 'Check-out successful', attendance });
        observer.complete();
      }).catch(error => {
        observer.error(error);
      });
    });
  }

  /**
   * Validate location for check-in
   */
  validateLocation(latitude: number, longitude: number, accuracy: number, attendanceSessionId?: string): Observable<LocationValidation> {
    // TODO: Implement Firebase-based location validation
    return of({ isValid: true, distance: 0, message: 'Location valid' });
  }

  /**
   * Get attendance records for a church
   */
  getChurchAttendance(churchId: string, startDate?: Date, endDate?: Date): Observable<AttendanceRecord[]> {
    const attendanceCollection = collection(this.firestore, 'attendance');
    let attendanceQuery = query(
      attendanceCollection,
      where('churchId', '==', churchId),
      orderBy('checkInTime', 'desc')
    );

    return new Observable(observer => {
      getDocs(attendanceQuery).then(async querySnapshot => {
        const attendanceRecords: AttendanceRecord[] = [];

        // Get church location for distance calculations
        const churchLocation = await this.getChurchLocation(churchId);

        // Process each attendance record
        for (const docSnapshot of querySnapshot.docs) {
          const data = docSnapshot.data() as Attendance;

          // Get member data
          const memberData = await this.getMemberData(data.memberId);

          // Calculate distance if church location and attendance coordinates are available
          let distance: number | undefined;
          if (churchLocation && data.latitude && data.longitude) {
            distance = this.calculateDistance(
              data.latitude,
              data.longitude,
              churchLocation.latitude,
              churchLocation.longitude
            );
          }

          const record: AttendanceRecord = {
            id: docSnapshot.id,
            memberId: data.memberId,
            checkInTime: data.checkInTime.toDate().toISOString(),
            checkOutTime: data.checkOutTime?.toDate().toISOString(),
            method: 'location',
            accuracy: data.locationAccuracy,
            distance: distance,
            member: memberData ? {
              id: memberData.id,
              firstName: memberData.firstName,
              lastName: memberData.lastName,
              email: memberData.email,
              fullName: memberData.fullName || `${memberData.firstName} ${memberData.lastName}`,
              profilePicture: memberData.profilePicture
            } : undefined
          };
          attendanceRecords.push(record);
        }

        observer.next(attendanceRecords);
        observer.complete();
      }).catch(error => {
        observer.error(error);
      });
    });
  }

  /**
   * Get live attendance for today
   */
  getLiveAttendance(churchId: string): Observable<LiveAttendance> {
    this.isLoading.set(true);

    const attendanceCollection = collection(this.firestore, 'attendance');
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const todayQuery = query(
      attendanceCollection,
      where('churchId', '==', churchId),
      where('checkInTime', '>=', Timestamp.fromDate(today)),
      orderBy('checkInTime', 'desc')
    );

    return new Observable(observer => {
      getDocs(todayQuery).then(async querySnapshot => {
        const recentCheckIns: AttendanceRecord[] = [];
        let totalCheckedIn = 0;
        let totalPresent = 0;

        // Get church location for distance calculations
        const churchLocation = await this.getChurchLocation(churchId);

        // Process each attendance record
        for (const docSnapshot of querySnapshot.docs) {
          const data = docSnapshot.data() as Attendance;
          totalCheckedIn++;
          if (data.status === 'present') {
            totalPresent++;
          }

          // Get member data
          const memberData = await this.getMemberData(data.memberId);

          // Calculate distance if church location and attendance coordinates are available
          let distance: number | undefined;
          if (churchLocation && data.latitude && data.longitude) {
            distance = this.calculateDistance(
              data.latitude,
              data.longitude,
              churchLocation.latitude,
              churchLocation.longitude
            );
          }

          const record: AttendanceRecord = {
            id: docSnapshot.id,
            memberId: data.memberId,
            checkInTime: data.checkInTime.toDate().toISOString(),
            checkOutTime: data.checkOutTime?.toDate().toISOString(),
            method: 'location',
            accuracy: data.locationAccuracy,
            distance: distance,
            member: memberData ? {
              id: memberData.id,
              firstName: memberData.firstName,
              lastName: memberData.lastName,
              email: memberData.email,
              fullName: memberData.fullName || `${memberData.firstName} ${memberData.lastName}`,
              profilePicture: memberData.profilePicture
            } : undefined
          };
          recentCheckIns.push(record);
        }

        const liveData: LiveAttendance = {
          totalPresent,
          totalCheckedIn,
          recentCheckIns: recentCheckIns.slice(0, 10), // Show only recent 10
          attendanceRate: totalCheckedIn > 0 ? (totalPresent / totalCheckedIn) * 100 : 0
        };

        this.liveAttendance.set(liveData);
        this.liveAttendanceSubject.next(liveData);
        this.isLoading.set(false);

        observer.next(liveData);
        observer.complete();
      }).catch(error => {
        this.isLoading.set(false);
        observer.error(error);
      });
    });
  }

  /**
   * Get attendance statistics
   */
  getAttendanceStats(churchId: string, days: number = 30): Observable<AttendanceStats> {
    this.isLoading.set(true);

    // TODO: Implement Firebase-based attendance stats
    const mockStats: AttendanceStats = {
      todayAttendance: 0,
      weeklyAttendance: 0,
      monthlyAttendance: 0,
      attendanceRate: 0,
      totalMembers: 0,
      averageAttendance: 0,
      trends: {
        daily: [],
        weekly: []
      }
    };

    this.attendanceStats.set(mockStats);
    this.attendanceStatsSubject.next(mockStats);
    this.isLoading.set(false);

    return of(mockStats);
  }

  /**
   * Get weekly attendance report
   */
  getWeeklyReport(churchId: string, startDate?: Date, endDate?: Date): Observable<WeeklyReport> {
    this.isLoading.set(true);

    const attendanceCollection = collection(this.firestore, 'attendance');
    const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // Default to 30 days ago
    const end = endDate || new Date();

    const attendanceQuery = query(
      attendanceCollection,
      where('churchId', '==', churchId),
      where('checkInTime', '>=', Timestamp.fromDate(start)),
      where('checkInTime', '<=', Timestamp.fromDate(end)),
      orderBy('checkInTime', 'desc')
    );

    return new Observable(observer => {
      getDocs(attendanceQuery).then(querySnapshot => {
        const attendanceRecords: any[] = [];
        querySnapshot.forEach(doc => {
          const data = doc.data() as Attendance;
          attendanceRecords.push({
            ...data,
            id: doc.id,
            checkInTime: data.checkInTime.toDate(),
            checkOutTime: data.checkOutTime?.toDate()
          });
        });

        // Process data into weekly report format
        const weeklyReport = this.processWeeklyData(attendanceRecords, start, end);
        this.isLoading.set(false);
        observer.next(weeklyReport);
        observer.complete();
      }).catch(error => {
        this.isLoading.set(false);
        observer.error(error);
      });
    });
  }

  /**
   * Get monthly attendance report
   */
  getMonthlyReport(churchId: string, startDate?: Date, endDate?: Date): Observable<MonthlyReport> {
    this.isLoading.set(true);

    const attendanceCollection = collection(this.firestore, 'attendance');
    const start = startDate || new Date(Date.now() - 90 * 24 * 60 * 60 * 1000); // Default to 90 days ago
    const end = endDate || new Date();

    const attendanceQuery = query(
      attendanceCollection,
      where('churchId', '==', churchId),
      where('checkInTime', '>=', Timestamp.fromDate(start)),
      where('checkInTime', '<=', Timestamp.fromDate(end)),
      orderBy('checkInTime', 'desc')
    );

    return new Observable(observer => {
      getDocs(attendanceQuery).then(querySnapshot => {
        const attendanceRecords: any[] = [];
        querySnapshot.forEach(doc => {
          const data = doc.data() as Attendance;
          attendanceRecords.push({
            ...data,
            id: doc.id,
            checkInTime: data.checkInTime.toDate(),
            checkOutTime: data.checkOutTime?.toDate()
          });
        });

        // Process data into monthly report format
        const monthlyReport = this.processMonthlyData(attendanceRecords, start, end);
        this.isLoading.set(false);
        observer.next(monthlyReport);
        observer.complete();
      }).catch(error => {
        this.isLoading.set(false);
        observer.error(error);
      });
    });
  }

  /**
   * Get attendance trends and analytics
   */
  getAttendanceTrends(churchId: string, period: string = 'monthly', days: number = 90): Observable<AttendanceTrends> {
    // TODO: Implement Firebase-based attendance trends
    return of({} as AttendanceTrends);
  }

  /**
   * Get export data for reports
   */
  getExportData(
    churchId: string,
    format: string = 'json',
    startDate?: Date,
    endDate?: Date,
    reportType: string = 'detailed'
  ): Observable<ExportData> {
    this.isLoading.set(true);

    const attendanceCollection = collection(this.firestore, 'attendance');
    const start = startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate || new Date();

    const attendanceQuery = query(
      attendanceCollection,
      where('churchId', '==', churchId),
      where('checkInTime', '>=', Timestamp.fromDate(start)),
      where('checkInTime', '<=', Timestamp.fromDate(end)),
      orderBy('checkInTime', 'desc')
    );

    return new Observable(observer => {
      getDocs(attendanceQuery).then(querySnapshot => {
        const attendanceRecords: any[] = [];
        querySnapshot.forEach(doc => {
          const data = doc.data() as Attendance;
          attendanceRecords.push({
            ...data,
            id: doc.id,
            checkInTime: data.checkInTime.toDate(),
            checkOutTime: data.checkOutTime?.toDate()
          });
        });

        // Process data based on report type
        let processedData: any;
        if (reportType === 'weekly') {
          processedData = this.processWeeklyData(attendanceRecords, start, end);
        } else if (reportType === 'monthly') {
          processedData = this.processMonthlyData(attendanceRecords, start, end);
        } else {
          // Detailed records export
          processedData = {
            records: attendanceRecords.map(record => ({
              date: record.checkInTime.toLocaleDateString(),
              memberName: `Member ${record.memberId}`, // TODO: Get actual member names
              checkInTime: record.checkInTime.toLocaleTimeString(),
              checkOutTime: record.checkOutTime?.toLocaleTimeString() || '',
              method: 'location',
              isVerified: false,
              isFlagged: false,
              accuracy: record.locationAccuracy
            }))
          };
        }

        const exportData: ExportData = {
          format,
          reportType,
          generatedAt: new Date().toISOString(),
          dateRange: {
            startDate: start.toISOString(),
            endDate: end.toISOString()
          },
          data: processedData
        };

        this.isLoading.set(false);
        observer.next(exportData);
        observer.complete();
      }).catch(error => {
        this.isLoading.set(false);
        observer.error(error);
      });
    });
  }

  /**
   * Refresh live attendance data
   */
  refreshLiveAttendance(churchId: string): void {
    this.getLiveAttendance(churchId).subscribe();
  }

  /**
   * Refresh attendance statistics
   */
  refreshAttendanceStats(churchId: string, days: number = 30): void {
    this.getAttendanceStats(churchId, days).subscribe();
  }

  // Observable getters for components that need them
  get liveAttendance$(): Observable<LiveAttendance | null> {
    return this.liveAttendanceSubject.asObservable();
  }

  get attendanceStats$(): Observable<AttendanceStats | null> {
    return this.attendanceStatsSubject.asObservable();
  }

  // Private helper methods for data processing
  private processWeeklyData(attendanceRecords: any[], startDate: Date, endDate: Date): WeeklyReport {
    const weeklyData: WeeklyReportData[] = [];
    const weekMap = new Map<string, any>();

    // Group attendance records by week
    attendanceRecords.forEach(record => {
      const checkInDate = new Date(record.checkInTime);
      const weekStart = this.getWeekStart(checkInDate);
      const weekKey = weekStart.toISOString().split('T')[0];

      if (!weekMap.has(weekKey)) {
        weekMap.set(weekKey, {
          weekStart: weekKey,
          totalAttendance: 0,
          uniqueMembers: new Set(),
          servicesHeld: new Set(),
          flaggedCount: 0,
          verifiedCount: 0
        });
      }

      const weekData = weekMap.get(weekKey);
      weekData.totalAttendance++;
      weekData.uniqueMembers.add(record.memberId);
      weekData.servicesHeld.add(checkInDate.toDateString());
    });

    // Convert map to array and calculate derived values
    weekMap.forEach((data, weekKey) => {
      const uniqueMembersCount = data.uniqueMembers.size;
      const servicesCount = data.servicesHeld.size;

      weeklyData.push({
        weekStart: weekKey,
        totalAttendance: data.totalAttendance,
        uniqueMembers: uniqueMembersCount,
        servicesHeld: servicesCount,
        flaggedCount: data.flaggedCount,
        verifiedCount: data.verifiedCount,
        verificationRate: data.totalAttendance > 0 ? (data.verifiedCount / data.totalAttendance) * 100 : 0,
        averagePerService: servicesCount > 0 ? data.totalAttendance / servicesCount : 0
      });
    });

    // Sort by week start date
    weeklyData.sort((a, b) => new Date(a.weekStart).getTime() - new Date(b.weekStart).getTime());

    // Calculate summary
    const totalAttendance = weeklyData.reduce((sum, week) => sum + week.totalAttendance, 0);
    const averageWeeklyAttendance = weeklyData.length > 0 ? totalAttendance / weeklyData.length : 0;
    const peakWeek = weeklyData.reduce((max, week) =>
      week.totalAttendance > max.totalAttendance ? week : max,
      weeklyData[0] || {} as WeeklyReportData
    );

    return {
      period: `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      totalWeeks: weeklyData.length,
      weeklyData,
      summary: {
        totalAttendance,
        averageWeeklyAttendance,
        peakWeek
      }
    };
  }

  private processMonthlyData(attendanceRecords: any[], startDate: Date, endDate: Date): MonthlyReport {
    const monthlyData: MonthlyReportData[] = [];
    const monthMap = new Map<string, any>();

    // Group attendance records by month
    attendanceRecords.forEach(record => {
      const checkInDate = new Date(record.checkInTime);
      const monthKey = `${checkInDate.getFullYear()}-${String(checkInDate.getMonth() + 1).padStart(2, '0')}`;
      const monthName = checkInDate.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });

      if (!monthMap.has(monthKey)) {
        monthMap.set(monthKey, {
          month: monthName,
          totalAttendance: 0,
          uniqueMembers: new Set(),
          servicesHeld: new Set(),
          weekendServices: new Set(),
          flaggedCount: 0,
          verifiedCount: 0
        });
      }

      const monthData = monthMap.get(monthKey);
      monthData.totalAttendance++;
      monthData.uniqueMembers.add(record.memberId);
      monthData.servicesHeld.add(checkInDate.toDateString());

      // Check if it's a weekend service (Saturday or Sunday)
      if (checkInDate.getDay() === 0 || checkInDate.getDay() === 6) {
        monthData.weekendServices.add(checkInDate.toDateString());
      }
    });

    // Convert map to array and calculate derived values
    monthMap.forEach((data) => {
      const uniqueMembersCount = data.uniqueMembers.size;
      const servicesCount = data.servicesHeld.size;
      const weekendServicesCount = data.weekendServices.size;

      monthlyData.push({
        month: data.month,
        totalAttendance: data.totalAttendance,
        uniqueMembers: uniqueMembersCount,
        servicesHeld: servicesCount,
        weekendServices: weekendServicesCount,
        flaggedCount: data.flaggedCount,
        verifiedCount: data.verifiedCount,
        verificationRate: data.totalAttendance > 0 ? (data.verifiedCount / data.totalAttendance) * 100 : 0,
        averagePerService: servicesCount > 0 ? data.totalAttendance / servicesCount : 0
      });
    });

    // Sort by month
    monthlyData.sort((a, b) => new Date(a.month).getTime() - new Date(b.month).getTime());

    // Calculate summary
    const totalAttendance = monthlyData.reduce((sum, month) => sum + month.totalAttendance, 0);
    const averageMonthlyAttendance = monthlyData.length > 0 ? totalAttendance / monthlyData.length : 0;
    const peakMonth = monthlyData.reduce((max, month) =>
      month.totalAttendance > max.totalAttendance ? month : max,
      monthlyData[0] || {} as MonthlyReportData
    );

    return {
      period: `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      totalMonths: monthlyData.length,
      monthlyData,
      summary: {
        totalAttendance,
        averageMonthlyAttendance,
        peakMonth
      }
    };
  }

  private getWeekStart(date: Date): Date {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day; // Adjust to get Sunday as start of week
    return new Date(d.setDate(diff));
  }
}
