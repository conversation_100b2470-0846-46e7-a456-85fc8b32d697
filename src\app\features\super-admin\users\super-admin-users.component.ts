import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-super-admin-users',
  standalone: true,
  imports: [CommonModule, MatCardModule, MatIconModule],
  template: `
    <div class="super-admin-users-container">
      <div class="page-header">
        <h1 class="page-title">
          <mat-icon>people</mat-icon>
          System Users Management
        </h1>
        <p class="page-subtitle">Manage all users across all churches</p>
      </div>

      <mat-card>
        <mat-card-content>
          <div class="coming-soon">
            <mat-icon>construction</mat-icon>
            <h3>Coming Soon</h3>
            <p>System users management functionality will be available soon.</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .super-admin-users-container {
      padding: 2rem;
      max-width: 1400px;
      margin: 0 auto;
    }

    .page-header {
      margin-bottom: 2rem;
    }

    .page-title {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      font-size: 2rem;
      font-weight: 700;
      color: #1a202c;
      margin: 0 0 0.5rem 0;
    }

    .page-title mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
      color: #667eea;
    }

    .page-subtitle {
      color: #718096;
      font-size: 1rem;
      margin: 0;
    }

    .coming-soon {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 4rem;
      text-align: center;
    }

    .coming-soon mat-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      color: #cbd5e0;
      margin-bottom: 1rem;
    }
  `]
})
export class SuperAdminUsersComponent {}
